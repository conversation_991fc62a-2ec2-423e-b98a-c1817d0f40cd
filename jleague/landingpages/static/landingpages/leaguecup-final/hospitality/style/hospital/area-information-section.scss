.area-information {
  text-align: center;
  padding: 120px 0 20px;
  position: relative;
  background-color: #000;

  &__container {
    width: 90%;
    max-width: 1172px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 80px;
  }

  .match_section {
    .title {
      font-family: 'Clash Grotesk', sans-serif;
      font-size: 70px;
      font-weight: 800;
      text-transform: uppercase;
      background: linear-gradient(to bottom, #F3E7CF, #DEAC63, #F3E7CF);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
      position: relative;
      display: inline-block;
      line-height: 60px;
      margin-bottom: 0px;
      @include sp {
        font-size: 36px;
      }
    }
    .title::after {
      content: "";
      display: block;
      width: 195px;
      height: 2px;
      margin: 10px auto 0;
      background: linear-gradient(to right, transparent, #b5823f, transparent)
    }
  }

  .title {
    font-family: 'Clash Grotesk', sans-serif;
    font-weight: 600;
    font-size: 48px;
    line-height: 48px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    text-transform: uppercase;
    margin: 0;
    color: #fff;
    margin-bottom: 32px;

    @include sp {
      font-size: 28px;
    }
  }

  .line {
    margin: 10px auto 0;
    height: 1px;
    background-color: #666;
  }

  .seat_map_section {
    .info {
      background-color: #fff;
      padding: 54px 53px;
      @include sp {
        padding: 0px;
        .seat_map__image {
          padding: 54px 53px 0px;
        }
        .list__style__disc {
          padding: 54px 10px 0px;
        }
      }
      .seat_map__image {
        @include sp { 
          display: none;
        }
      }
      .seat_map__image_sp {
        display: none;
        @include sp { 
          display: block;
          padding-top: 54px;
        }
      }
      border-radius: 16px;
    }
  }

  .activity_schedule_section {
    .list__style__disc {
      list-style: outside;
      font-family: 'Clash Grotesk', sans-serif;
      color: #fff;
    }
  }

  @include tablet {
    .price__image,
    .schedule__image,
    .schedule__image_sp,
    .match__image,
    .seat_map__image,
    .activities_schedule__image {
      width: 100%;
      height: 100%;
    }
  }
}

.list__style__disc {
  list-style: outside;
  font-family: 'Clash Grotesk', sans-serif;
  line-height: 25px;
}

.btn_expand {
  width: 100%;
  padding-top: 54px;
  padding-bottom: 54px;
  display: flex;
  justify-content: center;
  align-items: center;

  @include sp {
    padding-bottom: 54px !important;
  }

  .button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 22px;
    font-weight: bold;
    text-decoration: none;
    border-radius: 30px;
    transition: background-color 0.3s ease;
    max-width: 400px;
    @include sp {
      max-width: none;
      width: 99%;
    }
    @include sp-small {
      font-size: 1rem;
    }
    width: 100%;
    padding: 16px 0;
    position: relative;
  }

  .btn_white {
    background-color: #fff;
    color: #000;
    &:hover {
      background-color: #000;
    }
  }

  .btn_black {
    background-color: #fff;
    border: 1px solid #000;
    color: #000;
    opacity: 1;
    &:hover {
      background-color: #fff;
      color: #000;
    }
  }
}

.modal {
  display: none;
  position: fixed;
  z-index: 1;
  padding-top: 100px;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0,0,0);
  background-color: rgba(0,0,0,0.4);
}

.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 0px 20px 20px 20px;
  border: 1px solid #888;
  width: 80%;
  @include sp {
    width: 100%;
    border: 0px;
    padding: 0px;
  }
  .seat_map__image {
    @include sp { 
      display: none;
    }
  }
  .seat_map__image_sp {
    display: none;
    @include sp { 
      display: block;
      padding-bottom: 20px;
    }
  }
}

.close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}
