.activities {
  text-align: center;
  background: url("/static/landingpages/leaguecup-final/hospitality/images/bg/Activities-bg.jpg");
  padding: 200px 0 150px;
  position: relative;
  background-size: cover;

  @include sp {
    padding: 100px 0 150px;
  }

  &::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    bottom: 0;
    border-top: 60px solid transparent;
    border-left: 48vw solid transparent;
    border-right: 50vw solid #000;
    border-bottom: 60px solid #000;
    transform: rotate(180deg);
    bottom: inherit;
    top: 0;
  }

  &__container {
    width: 90%;
    max-width: 1172px;
    margin: 0 auto;
  }

  &__title {
    font-size: 82px;
    margin-bottom: 10px;
    color: #fff;
    font-family: 'Clash Grotesk', sans-serif;
    font-weight: 800;
    text-transform: uppercase;
    background: linear-gradient(to bottom, #F3E7CF, #DEAC63, #F3E7CF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    @include sp {
      font-size: 36px;
    }

    &::after {
      content: "";
      display: block;
      width: 195px;
      height: 2px;
      margin: 10px auto 0;
      background: linear-gradient(to right, transparent, #b5823f, transparent);
    }
  }

  &__restaurant {
    font-family: 'Clash Grotesk', sans-serif;
    font-weight: 600;
    font-size: 48px;
    line-height: 48px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    text-transform: uppercase;
    margin: 0;
    color: #fff;
    margin-bottom: 48px;

    @include sp {
      font-size: 36px;
    }
  }

  &__note {
    color: #ccc;
    margin-top: 30px;
    margin-bottom: 78px;
    font-family: 'Host Grotesk', sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 16px;
    line-height: 1;
    letter-spacing: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    @include sp {
      margin-top: 50px;
    }
  }

  &__gourmet {
    color: #ccc;
    margin-top: 30px;
    font-family: 'Host Grotesk', sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__link {
    color: #ccc;
    margin-bottom: 120px;
    font-family: 'Host Grotesk', sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    a {
      color: #ccc;
    }
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
    text-align: left;

    @include sp {
      display: flex;
      flex-direction: column;
    }
  }

  &__map iframe {
    @include sp {
      height: 350px;
    }
  }

  &__item {
    max-width: 566px;
  }

  &__image {
    width: 100%;
    height: 100%;
    max-width: 566px;
    max-height: 377px;
    display: block;
    border-radius: 16px;
  }

  &__subtitle {
    font-size: 20px;
    @include sp {
      font-size: 18px;
    }
    font-weight: bold;
    margin: 15px 0 10px;
    color: #DEAC63;
    font-family: 'Clash Grotesk', sans-serif;
  }

  &__description {
    font-size: 16px;
    @include sp {
      font-size: 14px;
    }
    line-height: 1.6;
    color: #fff;
    padding: 0 15px 0 0;
    font-family: 'Clash Grotesk', sans-serif;

    a {
      font-family: 'Host Grotesk', sans-serif;
      font-weight: 400;
      font-size: 20px;
      line-height: 100%;
      color: #FFFFFF;
      text-decoration: underline;
      text-decoration-style: solid;
    }
  }

  .interaction {
    display: flex;
    flex-direction: column;
    margin-top: 100px;

    &__title {
      font-size: 38px;
      font-weight: bold;
      margin-bottom: 68px;
      color: #fff;
    }

    &__grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 40px;
      text-align: left;

      @include sp {
        display: flex;
        flex-direction: column;
      }
    }

    &__item {
      max-width: 566px;
      max-height: 494px;
    }

    &__image {
      width: 100%;
      height: 100%;
      max-width: 566px;
      max-height: 377px;
      display: block;
      border-radius: 16px;
    }

    &__subtitle {
      font-size: 20px;
      font-weight: bold;
      margin: 15px 0 10px;
      color: #DEAC63;
      font-family: 'Clash Grotesk', sans-serif;
    }

    &__description {
      font-size: 14px;
      line-height: 1.6;
      color: #fff;
      padding: 0 15px 0 0;
      font-family: 'Clash Grotesk', sans-serif;
    }
  }

  .limit_good {
    display: flex;
    flex-direction: column;
    margin-top: 160px;

    &__title {
      font-size: 38px;
      font-weight: bold;
      margin-bottom: 68px;
      color: #fff;
    }

    &__grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 40px;
      text-align: left;

      @include sp {
        display: flex;
        flex-direction: column;
      }
    }

    &__item {
      max-width: 364px;
      max-height: 242px;

      @include sp {
        max-width: 100%;
        max-height: 100%;
      }
    }

    &__image {
      width: 100%;
      height: 100%;
      max-width: 364px;
      max-height: 242px;
      display: block;
      object-fit: contain;
      border-radius: 16px;

      @include sp {
        max-width: 100%;
        max-height: 100%;
      }
    }

    &__subtitle {
      font-size: 20px;
      font-weight: bold;
      margin: 15px 0 10px;
      color: #DEAC63;
      font-family: 'Clash Grotesk', sans-serif;
    }

    &__description {
      font-size: 14px;
      line-height: 1.6;
      color: #fff;
      padding: 0 15px 0 0;
      font-family: 'Clash Grotesk', sans-serif;
    }
  }

  &__btn_purchase {
    margin-top: 50px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;

    @include sp {
      margin-top: 50px;
    }
  }
}
