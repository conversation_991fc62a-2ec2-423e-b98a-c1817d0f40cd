{"version": 3, "file": "atropos.min.js", "names": ["$", "el", "sel", "querySelector", "removeUndefinedProps", "obj", "result", "Object", "keys", "for<PERSON>ach", "key", "defaults", "alwaysActive", "activeOffset", "shadowOffset", "shadowScale", "duration", "rotate", "rotateTouch", "rotateXMax", "rotateYMax", "rotateXInvert", "rotateYInvert", "stretchX", "stretchY", "stretchZ", "commonOrigin", "shadow", "highlight", "originalParams", "childrenRootEl", "rotateEl", "scaleEl", "innerEl", "elBoundingClientRect", "eventsElBoundingClientRect", "shadowEl", "highlightEl", "isScrolling", "clientXStart", "clientYStart", "queueFrameId", "_originalParams", "eventsEl", "isComponent", "self", "__atropos__", "params", "_extends", "onEnter", "onLeave", "onRotate", "destroyed", "isActive", "queue", "purge<PERSON><PERSON>ue", "requestAnimationFrame", "data", "element", "prop", "value", "style", "splice", "length", "created", "$setDuration", "push", "$setEasing", "$setTransform", "$setOpacity", "$on", "event", "handler", "props", "addEventListener", "$off", "removeEventListener", "setChildrenOffset", "_ref", "_ref$rotateXPercentag", "rotateXPercentage", "_ref$rotateYPercentag", "rotateYPercentage", "opacityOnly", "easeOut", "querySelectorAll", "$$", "childEl", "elementOpacity", "dataset", "atroposOpacity", "split", "map", "v", "parseFloat", "getOpacity", "childElOffset", "atroposOffset", "Number", "isNaN", "min", "max", "rotatePercentage", "Math", "abs", "setElements", "clientX", "clientY", "isMultiple", "getBoundingClientRect", "rect", "left", "width", "top", "height", "transform<PERSON><PERSON>in", "rotateX", "rotateY", "_elBoundingClientRect", "_eventsElBoundingClie", "parentTop", "parentLeft", "parentWidth", "parentHeight", "centerX", "centerY", "coordX", "coordY", "activate", "classList", "add", "onPointerEnter", "e", "undefined", "type", "pointerType", "preventDefault", "onTouchMove", "cancelable", "onPointerMove", "diffX", "diffY", "touchAngle", "atan2", "PI", "onPointerLeave", "remove", "onDocumentClick", "clickTarget", "target", "contains", "destroy", "cancelAnimationFrame", "document", "parentNode", "host", "assign", "createElement", "append<PERSON><PERSON><PERSON>", "createHighlight"], "sources": ["../src/atropos.js"], "sourcesContent": ["/* eslint-disable no-restricted-globals */\nconst $ = (el, sel) => el.querySelector(sel);\nconst $$ = (el, sel) => el.querySelectorAll(sel);\n\nconst removeUndefinedProps = (obj = {}) => {\n  const result = {};\n  Object.keys(obj).forEach((key) => {\n    if (typeof obj[key] !== 'undefined') result[key] = obj[key];\n  });\n  return result;\n};\nexport const defaults = {\n  alwaysActive: false,\n  activeOffset: 50,\n  shadowOffset: 50,\n  shadowScale: 1,\n  duration: 300,\n  rotate: true,\n  rotateTouch: true,\n  rotateXMax: 15,\n  rotateYMax: 15,\n  rotateXInvert: false,\n  rotateYInvert: false,\n  stretchX: 0,\n  stretchY: 0,\n  stretchZ: 0,\n  commonOrigin: true,\n  shadow: true,\n  highlight: true,\n};\nfunction Atropos(originalParams = {}) {\n  let { el, eventsEl } = originalParams;\n  const { isComponent } = originalParams;\n  let childrenRootEl;\n  const self = {\n    __atropos__: true,\n    params: {\n      ...defaults,\n      onEnter: null,\n      onLeave: null,\n      onRotate: null,\n      ...removeUndefinedProps(originalParams || {}),\n    },\n    destroyed: false,\n    isActive: false,\n  };\n\n  const { params } = self;\n\n  let rotateEl;\n  let scaleEl;\n  let innerEl;\n\n  let elBoundingClientRect;\n  let eventsElBoundingClientRect;\n\n  let shadowEl;\n  let highlightEl;\n\n  let isScrolling;\n  let clientXStart;\n  let clientYStart;\n\n  const queue = [];\n  let queueFrameId;\n  const purgeQueue = () => {\n    queueFrameId = requestAnimationFrame(() => {\n      queue.forEach((data) => {\n        if (typeof data === 'function') {\n          data();\n        } else {\n          const { element, prop, value } = data;\n          element.style[prop] = value;\n        }\n      });\n      queue.splice(0, queue.length);\n      purgeQueue();\n    });\n  };\n  purgeQueue();\n\n  const $setDuration = (element, value) => {\n    queue.push({ element, prop: 'transitionDuration', value });\n  };\n  const $setEasing = (element, value) => {\n    queue.push({ element, prop: 'transitionTimingFunction', value });\n  };\n  const $setTransform = (element, value) => {\n    queue.push({ element, prop: 'transform', value });\n  };\n  const $setOpacity = (element, value) => {\n    queue.push({ element, prop: 'opacity', value });\n  };\n  const $setOrigin = (element, value) => {\n    queue.push({ element, prop: 'transformOrigin', value });\n  };\n  const $on = (element, event, handler, props) => element.addEventListener(event, handler, props);\n  const $off = (element, event, handler, props) =>\n    element.removeEventListener(event, handler, props);\n\n  const createShadow = () => {\n    let created;\n    shadowEl = $(el, '.atropos-shadow');\n    if (!shadowEl) {\n      shadowEl = document.createElement('span');\n      shadowEl.classList.add('atropos-shadow');\n      created = true;\n    }\n    $setTransform(\n      shadowEl,\n      `translate3d(0,0,-${params.shadowOffset}px) scale(${params.shadowScale})`,\n    );\n    if (created) {\n      rotateEl.appendChild(shadowEl);\n    }\n  };\n  const createHighlight = () => {\n    let created;\n    highlightEl = $(el, '.atropos-highlight');\n    if (!highlightEl) {\n      highlightEl = document.createElement('span');\n      highlightEl.classList.add('atropos-highlight');\n      created = true;\n    }\n\n    $setTransform(highlightEl, `translate3d(0,0,0)`);\n    if (created) {\n      innerEl.appendChild(highlightEl);\n    }\n  };\n\n  const setChildrenOffset = ({\n    rotateXPercentage = 0,\n    rotateYPercentage = 0,\n    duration,\n    opacityOnly,\n    easeOut,\n  }) => {\n    const getOpacity = (element) => {\n      if (element.dataset.atroposOpacity && typeof element.dataset.atroposOpacity === 'string') {\n        return element.dataset.atroposOpacity.split(';').map((v) => parseFloat(v));\n      }\n      return undefined;\n    };\n\n    $$(childrenRootEl, '[data-atropos-offset], [data-atropos-opacity]').forEach((childEl) => {\n      $setDuration(childEl, duration);\n      $setEasing(childEl, easeOut ? 'ease-out' : '');\n      const elementOpacity = getOpacity(childEl);\n      if (rotateXPercentage === 0 && rotateYPercentage === 0) {\n        if (!opacityOnly) $setTransform(childEl, `translate3d(0, 0, 0)`);\n        if (elementOpacity) $setOpacity(childEl, elementOpacity[0]);\n      } else {\n        const childElOffset = parseFloat(childEl.dataset.atroposOffset) / 100;\n        if (!Number.isNaN(childElOffset) && !opacityOnly) {\n          $setTransform(\n            childEl,\n            `translate3d(${-rotateYPercentage * -childElOffset}%, ${\n              rotateXPercentage * -childElOffset\n            }%, 0)`,\n          );\n        }\n        if (elementOpacity) {\n          const [min, max] = elementOpacity;\n          const rotatePercentage = Math.max(\n            Math.abs(rotateXPercentage),\n            Math.abs(rotateYPercentage),\n          );\n          $setOpacity(childEl, min + ((max - min) * rotatePercentage) / 100);\n        }\n      }\n    });\n  };\n\n  const setElements = (clientX, clientY) => {\n    const isMultiple = el !== eventsEl;\n    if (!elBoundingClientRect) {\n      elBoundingClientRect = el.getBoundingClientRect();\n    }\n    if (isMultiple && !eventsElBoundingClientRect) {\n      eventsElBoundingClientRect = eventsEl.getBoundingClientRect();\n    }\n    if (typeof clientX === 'undefined' && typeof clientY === 'undefined') {\n      const rect = isMultiple ? eventsElBoundingClientRect : elBoundingClientRect;\n      clientX = rect.left + rect.width / 2;\n      clientY = rect.top + rect.height / 2;\n    }\n\n    let rotateX = 0;\n    let rotateY = 0;\n    const { top, left, width, height } = elBoundingClientRect;\n    let transformOrigin;\n    if (!isMultiple) {\n      const centerX = width / 2;\n      const centerY = height / 2;\n\n      const coordX = clientX - left;\n      const coordY = clientY - top;\n\n      rotateY = ((params.rotateYMax * (coordX - centerX)) / (width / 2)) * -1;\n      rotateX = (params.rotateXMax * (coordY - centerY)) / (height / 2);\n    } else {\n      const {\n        top: parentTop,\n        left: parentLeft,\n        width: parentWidth,\n        height: parentHeight,\n      } = eventsElBoundingClientRect;\n      const offsetLeft = left - parentLeft;\n      const offsetTop = top - parentTop;\n\n      const centerX = width / 2 + offsetLeft;\n      const centerY = height / 2 + offsetTop;\n\n      const coordX = clientX - parentLeft;\n      const coordY = clientY - parentTop;\n\n      rotateY = ((params.rotateYMax * (coordX - centerX)) / (parentWidth - width / 2)) * -1;\n      rotateX = (params.rotateXMax * (coordY - centerY)) / (parentHeight - height / 2);\n      transformOrigin = `${clientX - left}px ${clientY - top}px`;\n    }\n\n    rotateX = Math.min(Math.max(-rotateX, -params.rotateXMax), params.rotateXMax);\n    if (params.rotateXInvert) rotateX = -rotateX;\n    rotateY = Math.min(Math.max(-rotateY, -params.rotateYMax), params.rotateYMax);\n    if (params.rotateYInvert) rotateY = -rotateY;\n\n    const rotateXPercentage = (rotateX / params.rotateXMax) * 100;\n    const rotateYPercentage = (rotateY / params.rotateYMax) * 100;\n\n    const stretchX =\n      (isMultiple ? (rotateYPercentage / 100) * params.stretchX : 0) *\n      (params.rotateYInvert ? -1 : 1);\n    const stretchY =\n      (isMultiple ? (rotateXPercentage / 100) * params.stretchY : 0) *\n      (params.rotateXInvert ? -1 : 1);\n    const stretchZ = isMultiple\n      ? (Math.max(Math.abs(rotateXPercentage), Math.abs(rotateYPercentage)) / 100) * params.stretchZ\n      : 0;\n    $setTransform(\n      rotateEl,\n      `translate3d(${stretchX}%, ${-stretchY}%, ${-stretchZ}px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`,\n    );\n    if (transformOrigin && params.commonOrigin) {\n      $setOrigin(rotateEl, transformOrigin);\n    }\n\n    if (highlightEl) {\n      $setDuration(highlightEl, `${params.duration}ms`);\n      $setEasing(highlightEl, 'ease-out');\n      $setTransform(\n        highlightEl,\n        `translate3d(${-rotateYPercentage * 0.25}%, ${rotateXPercentage * 0.25}%, 0)`,\n      );\n      $setOpacity(\n        highlightEl,\n        Math.max(Math.abs(rotateXPercentage), Math.abs(rotateYPercentage)) / 100,\n      );\n    }\n\n    setChildrenOffset({\n      rotateXPercentage,\n      rotateYPercentage,\n      duration: `${params.duration}ms`,\n      easeOut: true,\n    });\n\n    if (typeof params.onRotate === 'function') params.onRotate(rotateX, rotateY);\n  };\n\n  const activate = () => {\n    queue.push(() => el.classList.add('atropos-active'));\n    $setDuration(rotateEl, `${params.duration}ms`);\n    $setEasing(rotateEl, 'ease-out');\n    $setTransform(scaleEl, `translate3d(0,0, ${params.activeOffset}px)`);\n    $setDuration(scaleEl, `${params.duration}ms`);\n    $setEasing(scaleEl, 'ease-out');\n    if (shadowEl) {\n      $setDuration(shadowEl, `${params.duration}ms`);\n      $setEasing(shadowEl, 'ease-out');\n    }\n\n    self.isActive = true;\n  };\n\n  const onPointerEnter = (e) => {\n    isScrolling = undefined;\n    if (e.type === 'pointerdown' && e.pointerType === 'mouse') return;\n    if (e.type === 'pointerenter' && e.pointerType !== 'mouse') return;\n    if (e.type === 'pointerdown') {\n      e.preventDefault();\n    }\n    clientXStart = e.clientX;\n    clientYStart = e.clientY;\n\n    if (params.alwaysActive) {\n      elBoundingClientRect = undefined;\n      eventsElBoundingClientRect = undefined;\n      return;\n    }\n    activate();\n    if (typeof params.onEnter === 'function') params.onEnter();\n  };\n\n  const onTouchMove = (e) => {\n    if (isScrolling === false && e.cancelable) {\n      e.preventDefault();\n    }\n  };\n\n  const onPointerMove = (e) => {\n    if (!params.rotate || !self.isActive) return;\n    if (e.pointerType !== 'mouse') {\n      if (!params.rotateTouch) return;\n      e.preventDefault();\n    }\n    const { clientX, clientY } = e;\n\n    const diffX = clientX - clientXStart;\n    const diffY = clientY - clientYStart;\n    if (\n      typeof params.rotateTouch === 'string' &&\n      (diffX !== 0 || diffY !== 0) &&\n      typeof isScrolling === 'undefined'\n    ) {\n      if (diffX * diffX + diffY * diffY >= 25) {\n        const touchAngle = (Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180) / Math.PI;\n        isScrolling = params.rotateTouch === 'scroll-y' ? touchAngle > 45 : 90 - touchAngle > 45;\n      }\n      if (isScrolling === false) {\n        el.classList.add('atropos-rotate-touch');\n        if (e.cancelable) {\n          e.preventDefault();\n        }\n      }\n    }\n    if (e.pointerType !== 'mouse' && isScrolling) {\n      return;\n    }\n    setElements(clientX, clientY);\n  };\n\n  const onPointerLeave = (e) => {\n    elBoundingClientRect = undefined;\n    eventsElBoundingClientRect = undefined;\n    if (!self.isActive) return;\n    if (e && e.type === 'pointerup' && e.pointerType === 'mouse') return;\n    if (e && e.type === 'pointerleave' && e.pointerType !== 'mouse') return;\n    if (typeof params.rotateTouch === 'string' && isScrolling) {\n      el.classList.remove('atropos-rotate-touch');\n    }\n\n    if (params.alwaysActive) {\n      setElements();\n      if (typeof params.onRotate === 'function') params.onRotate(0, 0);\n      if (typeof params.onLeave === 'function') params.onLeave();\n      return;\n    }\n\n    queue.push(() => el.classList.remove('atropos-active'));\n    $setDuration(scaleEl, `${params.duration}ms`);\n    $setEasing(scaleEl, '');\n    $setTransform(scaleEl, `translate3d(0,0, ${0}px)`);\n    if (shadowEl) {\n      $setDuration(shadowEl, `${params.duration}ms`);\n      $setEasing(shadowEl, '');\n    }\n    if (highlightEl) {\n      $setDuration(highlightEl, `${params.duration}ms`);\n      $setEasing(highlightEl, '');\n      $setTransform(highlightEl, `translate3d(0, 0, 0)`);\n      $setOpacity(highlightEl, 0);\n    }\n    $setDuration(rotateEl, `${params.duration}ms`);\n    $setEasing(rotateEl, '');\n    $setTransform(rotateEl, `translate3d(0,0,0) rotateX(0deg) rotateY(0deg)`);\n\n    setChildrenOffset({ duration: `${params.duration}ms` });\n\n    self.isActive = false;\n    if (typeof params.onRotate === 'function') params.onRotate(0, 0);\n    if (typeof params.onLeave === 'function') params.onLeave();\n  };\n\n  const onDocumentClick = (e) => {\n    const clickTarget = e.target;\n    if (!eventsEl.contains(clickTarget) && clickTarget !== eventsEl && self.isActive) {\n      onPointerLeave();\n    }\n  };\n\n  const initDOM = () => {\n    if (typeof el === 'string') {\n      el = $(document, el);\n    }\n    if (!el) return;\n\n    // eslint-disable-next-line\n    if (el.__atropos__) return;\n\n    if (typeof eventsEl !== 'undefined') {\n      if (typeof eventsEl === 'string') {\n        eventsEl = $(document, eventsEl);\n      }\n    } else {\n      eventsEl = el;\n    }\n    childrenRootEl = isComponent ? el.parentNode.host : el;\n\n    Object.assign(self, {\n      el,\n    });\n\n    rotateEl = $(el, '.atropos-rotate');\n    scaleEl = $(el, '.atropos-scale');\n    innerEl = $(el, '.atropos-inner');\n\n    // eslint-disable-next-line\n    el.__atropos__ = self;\n  };\n\n  const init = () => {\n    initDOM();\n    if (!el || !eventsEl) return;\n    if (params.shadow) {\n      createShadow();\n    }\n    if (params.highlight) {\n      createHighlight();\n    }\n    if (params.rotateTouch) {\n      if (typeof params.rotateTouch === 'string') {\n        el.classList.add(`atropos-rotate-touch-${params.rotateTouch}`);\n      } else {\n        el.classList.add('atropos-rotate-touch');\n      }\n    }\n    if ($(childrenRootEl, '[data-atropos-opacity]')) {\n      setChildrenOffset({ opacityOnly: true });\n    }\n    $on(document, 'click', onDocumentClick);\n    $on(eventsEl, 'pointerdown', onPointerEnter);\n    $on(eventsEl, 'pointerenter', onPointerEnter);\n    $on(eventsEl, 'pointermove', onPointerMove);\n    $on(eventsEl, 'touchmove', onTouchMove);\n    $on(eventsEl, 'pointerleave', onPointerLeave);\n    $on(eventsEl, 'pointerup', onPointerLeave);\n    $on(eventsEl, 'lostpointercapture', onPointerLeave);\n\n    if (params.alwaysActive) {\n      activate();\n      setElements();\n    }\n  };\n\n  const destroy = () => {\n    self.destroyed = true;\n    cancelAnimationFrame(queueFrameId);\n    $off(document, 'click', onDocumentClick);\n    $off(eventsEl, 'pointerdown', onPointerEnter);\n    $off(eventsEl, 'pointerenter', onPointerEnter);\n    $off(eventsEl, 'pointermove', onPointerMove);\n    $off(eventsEl, 'touchmove', onTouchMove);\n    $off(eventsEl, 'pointerleave', onPointerLeave);\n    $off(eventsEl, 'pointerup', onPointerLeave);\n    $off(eventsEl, 'lostpointercapture', onPointerLeave);\n    // eslint-disable-next-line\n    delete el.__atropos__;\n  };\n\n  self.destroy = destroy;\n\n  init();\n\n  // eslint-disable-next-line\n  return self;\n}\nexport { Atropos };\nexport default Atropos;\n"], "mappings": ";;;;;;;;;;;;wQACA,IAAMA,EAAI,SAACC,EAAIC,GAAG,OAAKD,EAAGE,cAAcD,EAAI,EAGtCE,EAAuB,SAACC,QAAG,IAAHA,MAAM,IAClC,IAAMC,EAAS,GAIf,OAHAC,OAAOC,KAAKH,GAAKI,SAAQ,SAACC,QACA,IAAbL,EAAIK,KAAsBJ,EAAOI,GAAOL,EAAIK,GACzD,IACOJ,CACT,EACqBK,EAAG,CACtBC,cAAc,EACdC,aAAc,GACdC,aAAc,GACdC,YAAa,EACbC,SAAU,IACVC,QAAQ,EACRC,aAAa,EACbC,WAAY,GACZC,WAAY,GACZC,eAAe,EACfC,eAAe,EACfC,SAAU,EACVC,SAAU,EACVC,SAAU,EACVC,cAAc,EACdC,QAAQ,EACRC,WAAW,G,OAEb,SAAiBC,QAAc,IAAdA,MAAiB,IAChC,IAEIC,EAgBAC,EACAC,EACAC,EAEAC,EACAC,EAEAC,EACAC,EAEAC,EACAC,EACAC,EAGAC,EAjCJC,EAAuBb,EAAjB5B,EAAEyC,EAAFzC,GAAI0C,EAAQD,EAARC,SACFC,EAAgBf,EAAhBe,YAEFC,EAAO,CACXC,aAAa,EACbC,OAAMC,EAAA,GACDrC,EAAQ,CACXsC,QAAS,KACTC,QAAS,KACTC,SAAU,MACP/C,EAAqByB,GAAkB,KAE5CuB,WAAW,EACXC,UAAU,GAGJN,EAAWF,EAAXE,OAgBFO,EAAQ,IAEK,SAAbC,IACJd,EAAee,uBAAsB,WACnCF,EAAM7C,SAAQ,SAACgD,GACb,GAAoB,mBAATA,EACTA,QACK,CACL,IAAQC,EAAyBD,EAAzBC,QAASC,EAAgBF,EAAhBE,KAAMC,EAAUH,EAAVG,MACvBF,EAAQG,MAAMF,GAAQC,CACxB,CACF,IACAN,EAAMQ,OAAO,EAAGR,EAAMS,QACtBR,GACF,G,CAEFA,GAEA,IAoBMS,EApBAC,EAAe,SAACP,EAASE,GAC7BN,EAAMY,KAAK,CAAER,UAASC,KAAM,qBAAsBC,S,EAE9CO,EAAa,SAACT,EAASE,GAC3BN,EAAMY,KAAK,CAAER,UAASC,KAAM,2BAA4BC,S,EAEpDQ,EAAgB,SAACV,EAASE,GAC9BN,EAAMY,KAAK,CAAER,UAASC,KAAM,YAAaC,S,EAErCS,EAAc,SAACX,EAASE,GAC5BN,EAAMY,KAAK,CAAER,UAASC,KAAM,UAAWC,S,EAKnCU,EAAM,SAACZ,EAASa,EAAOC,EAASC,GAAK,OAAKf,EAAQgB,iBAAiBH,EAAOC,EAASC,EAAM,EACzFE,EAAO,SAACjB,EAASa,EAAOC,EAASC,GAAK,OAC1Cf,EAAQkB,oBAAoBL,EAAOC,EAASC,EAAM,EAiC9CI,EAAoB,SAAHC,GAMjB,IAAAC,EAAAD,EALJE,yBAAoB,IAAHD,EAAG,EAACA,EAAAE,EAAAH,EACrBI,yBAAoB,IAAHD,EAAG,EAACA,EACrBjE,EAAQ8D,EAAR9D,SACAmE,EAAWL,EAAXK,YACAC,EAAON,EAAPM,SAtIO,SAACnF,EAAIC,GAAG,OAAKD,EAAGoF,iBAAiBnF,EAAI,EA+I5CoF,CAAGxD,EAAgB,iDAAiDrB,SAAQ,SAAC8E,GAC3EtB,EAAasB,EAASvE,GACtBmD,EAAWoB,EAASH,EAAU,WAAa,IAC3C,IAAMI,EAVW,SAAC9B,GAClB,GAAIA,EAAQ+B,QAAQC,gBAA4D,iBAAnChC,EAAQ+B,QAAQC,eAC3D,OAAOhC,EAAQ+B,QAAQC,eAAeC,MAAM,KAAKC,KAAI,SAACC,GAAC,OAAKC,WAAWD,E,IAQlDE,CAAWR,GAClC,GAA0B,IAAtBP,GAAiD,IAAtBE,EACxBC,GAAaf,EAAcmB,EAAO,wBACnCC,GAAgBnB,EAAYkB,EAASC,EAAe,QACnD,CACL,IAAMQ,EAAgBF,WAAWP,EAAQE,QAAQQ,eAAiB,IASlE,GARKC,OAAOC,MAAMH,IAAmBb,GACnCf,EACEmB,EACe,gBAACL,GAAqBc,EAAa,MAChDhB,GAAqBgB,EAAa,SAIpCR,EAAgB,CAClB,IAAOY,EAAYZ,EAAc,GAArBa,EAAOb,EAAc,GAC3Bc,EAAmBC,KAAKF,IAC5BE,KAAKC,IAAIxB,GACTuB,KAAKC,IAAItB,IAEXb,EAAYkB,EAASa,GAAQC,EAAMD,GAAOE,EAAoB,IAChE,CACF,CACF,G,EAGIG,EAAc,SAACC,EAASC,GAC5B,IAAMC,EAAa3G,IAAO0C,EAO1B,GANKT,IACHA,EAAuBjC,EAAG4G,yBAExBD,IAAezE,IACjBA,EAA6BQ,EAASkE,8BAEjB,IAAZH,QAA8C,IAAZC,EAAyB,CACpE,IAAMG,EAAOF,EAAazE,EAA6BD,EACvDwE,EAAUI,EAAKC,KAAOD,EAAKE,MAAQ,EACnCL,EAAUG,EAAKG,IAAMH,EAAKI,OAAS,CACrC,CAEA,IAGIC,EAHAC,EAAU,EACVC,EAAU,EACdC,EAAqCpF,EAA7B+E,EAAGK,EAAHL,IAAKF,EAAIO,EAAJP,KAAMC,EAAKM,EAALN,MAAOE,EAAMI,EAANJ,OAE1B,GAAKN,EASE,CACL,IAAAW,EAKIpF,EAJGqF,EAASD,EAAdN,IACMQ,EAAUF,EAAhBR,KACOW,EAAWH,EAAlBP,MACQW,EAAYJ,EAApBL,OAKIU,EAAUZ,EAAQ,GAHLD,EAAOU,GAIpBI,EAAUX,EAAS,GAHPD,EAAMO,GAKlBM,EAASpB,EAAUe,EACnBM,EAASpB,EAAUa,EAEzBH,EAAYtE,EAAO3B,YAAc0G,EAASF,IAAaF,EAAcV,EAAQ,IAAO,EACpFI,EAAWrE,EAAO5B,YAAc4G,EAASF,IAAaF,EAAeT,EAAS,GAC9EC,EAAqBT,EAAUK,EAAI,OAAMJ,EAAUM,GAAO,IAC5D,KA5BiB,CACf,IAAMW,EAAUZ,EAAQ,EAClBa,EAAUX,EAAS,EAEnBY,EAASpB,EAAUK,EACnBgB,EAASpB,EAAUM,EAEzBI,EAAYtE,EAAO3B,YAAc0G,EAASF,IAAaZ,EAAQ,IAAO,EACtEI,EAAWrE,EAAO5B,YAAc4G,EAASF,IAAaX,EAAS,EACjE,CAqBAE,EAAUb,KAAKH,IAAIG,KAAKF,KAAKe,GAAUrE,EAAO5B,YAAa4B,EAAO5B,YAC9D4B,EAAO1B,gBAAe+F,GAAWA,GACrCC,EAAUd,KAAKH,IAAIG,KAAKF,KAAKgB,GAAUtE,EAAO3B,YAAa2B,EAAO3B,YAC9D2B,EAAOzB,gBAAe+F,GAAWA,GAErC,IAtIkB3D,EAASE,EAsIrBoB,EAAqBoC,EAAUrE,EAAO5B,WAAc,IACpD+D,EAAqBmC,EAAUtE,EAAO3B,WAAc,IAEpDG,GACHqF,EAAc1B,EAAoB,IAAOnC,EAAOxB,SAAW,IAC3DwB,EAAOzB,eAAiB,EAAI,GACzBE,GACHoF,EAAc5B,EAAoB,IAAOjC,EAAOvB,SAAW,IAC3DuB,EAAO1B,eAAiB,EAAI,GACzBI,EAAWmF,EACZL,KAAKF,IAAIE,KAAKC,IAAIxB,GAAoBuB,KAAKC,IAAItB,IAAsB,IAAOnC,EAAOtB,SACpF,EACJ2C,EACErC,EACeR,iBAAQ,OAAOC,EAAc,OAACC,EAAuB2F,iBAAuBC,kBAAO,QAEhGF,GAAmBpE,EAAOrB,eAtJZgC,EAuJL3B,EAvJc6B,EAuJJuD,EAtJvB7D,EAAMY,KAAK,CAAER,UAASC,KAAM,kBAAmBC,WAyJ3CvB,IACF4B,EAAa5B,EAAgBU,EAAO/B,SAAQ,MAC5CmD,EAAW9B,EAAa,YACxB+B,EACE/B,EACe,eAAqB,KAApB6C,EAAwB,MAA0B,IAApBF,EAAwB,SAExEX,EACEhC,EACAkE,KAAKF,IAAIE,KAAKC,IAAIxB,GAAoBuB,KAAKC,IAAItB,IAAsB,MAIzEL,EAAkB,CAChBG,oBACAE,oBACAlE,SAAa+B,EAAO/B,SAAY,KAChCoE,SAAS,IAGoB,mBAApBrC,EAAOI,UAAyBJ,EAAOI,SAASiE,EAASC,E,EAGhEW,EAAW,WACf1E,EAAMY,MAAK,kBAAMjE,EAAGgI,UAAUC,IAAI,iB,IAClCjE,EAAalC,EAAagB,EAAO/B,SAAQ,MACzCmD,EAAWpC,EAAU,YACrBqC,EAAcpC,EAAO,oBAAsBe,EAAOlC,aAAY,OAC9DoD,EAAajC,EAAYe,EAAO/B,SAAQ,MACxCmD,EAAWnC,EAAS,YAChBI,IACF6B,EAAa7B,EAAaW,EAAO/B,SAAQ,MACzCmD,EAAW/B,EAAU,aAGvBS,EAAKQ,UAAW,C,EAGZ8E,EAAiB,SAACC,GAEtB,GADA9F,OAAc+F,IACC,gBAAXD,EAAEE,MAA4C,UAAlBF,EAAEG,aACnB,iBAAXH,EAAEE,MAA6C,UAAlBF,EAAEG,aAAnC,CAOA,GANe,gBAAXH,EAAEE,MACJF,EAAEI,iBAEJjG,EAAe6F,EAAE1B,QACjBlE,EAAe4F,EAAEzB,QAEb5D,EAAOnC,aAGT,OAFAsB,OAAuBmG,OACvBlG,OAA6BkG,GAG/BL,IAC8B,mBAAnBjF,EAAOE,SAAwBF,EAAOE,SAbW,C,EAgBxDwF,EAAc,SAACL,IACC,IAAhB9F,GAAyB8F,EAAEM,YAC7BN,EAAEI,gB,EAIAG,EAAgB,SAACP,GACrB,GAAKrF,EAAO9B,QAAW4B,EAAKQ,SAA5B,CACA,GAAsB,UAAlB+E,EAAEG,YAAyB,CAC7B,IAAKxF,EAAO7B,YAAa,OACzBkH,EAAEI,gBACJ,CACA,IAAQ9B,EAAqB0B,EAArB1B,QAASC,EAAYyB,EAAZzB,QAEXiC,EAAQlC,EAAUnE,EAClBsG,EAAQlC,EAAUnE,EACxB,GACgC,iBAAvBO,EAAO7B,cACH,IAAV0H,GAAyB,IAAVC,SACO,IAAhBvG,EACP,CACA,GAAIsG,EAAQA,EAAQC,EAAQA,GAAS,GAAI,CACvC,IAAMC,EAA6D,IAA/CvC,KAAKwC,MAAMxC,KAAKC,IAAIqC,GAAQtC,KAAKC,IAAIoC,IAAiBrC,KAAKyC,GAC/E1G,EAAqC,aAAvBS,EAAO7B,YAA6B4H,EAAa,GAAK,GAAKA,EAAa,EACxF,EACoB,IAAhBxG,IACFrC,EAAGgI,UAAUC,IAAI,wBACbE,EAAEM,YACJN,EAAEI,iBAGR,CACsB,UAAlBJ,EAAEG,aAA2BjG,GAGjCmE,EAAYC,EAASC,EA5BiB,C,EA+BlCsC,EAAiB,SAACb,GAGtB,GAFAlG,OAAuBmG,EACvBlG,OAA6BkG,EACxBxF,EAAKQ,YACN+E,GAAgB,cAAXA,EAAEE,MAA0C,UAAlBF,EAAEG,aACjCH,GAAgB,iBAAXA,EAAEE,MAA6C,UAAlBF,EAAEG,aAAxC,CAKA,GAJkC,iBAAvBxF,EAAO7B,aAA4BoB,GAC5CrC,EAAGgI,UAAUiB,OAAO,wBAGlBnG,EAAOnC,aAIT,OAHA6F,IAC+B,mBAApB1D,EAAOI,UAAyBJ,EAAOI,SAAS,EAAG,QAChC,mBAAnBJ,EAAOG,SAAwBH,EAAOG,WAInDI,EAAMY,MAAK,kBAAMjE,EAAGgI,UAAUiB,OAAO,iB,IACrCjF,EAAajC,EAAYe,EAAO/B,SAAQ,MACxCmD,EAAWnC,EAAS,IACpBoC,EAAcpC,EAA6B,yBACvCI,IACF6B,EAAa7B,EAAaW,EAAO/B,SAAQ,MACzCmD,EAAW/B,EAAU,KAEnBC,IACF4B,EAAa5B,EAAgBU,EAAO/B,SAAQ,MAC5CmD,EAAW9B,EAAa,IACxB+B,EAAc/B,EAAW,wBACzBgC,EAAYhC,EAAa,IAE3B4B,EAAalC,EAAagB,EAAO/B,SAAQ,MACzCmD,EAAWpC,EAAU,IACrBqC,EAAcrC,EAAQ,kDAEtB8C,EAAkB,CAAE7D,SAAa+B,EAAO/B,SAAQ,OAEhD6B,EAAKQ,UAAW,EACe,mBAApBN,EAAOI,UAAyBJ,EAAOI,SAAS,EAAG,GAChC,mBAAnBJ,EAAOG,SAAwBH,EAAOG,SAlCgB,C,EAqC7DiG,EAAkB,SAACf,GACvB,IAAMgB,EAAchB,EAAEiB,QACjB1G,EAAS2G,SAASF,IAAgBA,IAAgBzG,GAAYE,EAAKQ,UACtE4F,G,EAwFJ,OALApG,EAAK0G,QAfW,WACd1G,EAAKO,WAAY,EACjBoG,qBAAqB/G,GACrBkC,EAAK8E,SAAU,QAASN,GACxBxE,EAAKhC,EAAU,cAAewF,GAC9BxD,EAAKhC,EAAU,eAAgBwF,GAC/BxD,EAAKhC,EAAU,cAAegG,GAC9BhE,EAAKhC,EAAU,YAAa8F,GAC5B9D,EAAKhC,EAAU,eAAgBsG,GAC/BtE,EAAKhC,EAAU,YAAasG,GAC5BtE,EAAKhC,EAAU,qBAAsBsG,UAE9BhJ,EAAG6C,W,EA3EQ,iBAAP7C,IACTA,EAAKD,EAAEyJ,SAAUxJ,IAEdA,IAGDA,EAAG6C,mBAEiB,IAAbH,EACe,iBAAbA,IACTA,EAAW3C,EAAEyJ,SAAU9G,IAGzBA,EAAW1C,EAEb6B,EAAiBc,EAAc3C,EAAGyJ,WAAWC,KAAO1J,EAEpDM,OAAOqJ,OAAO/G,EAAM,CAClB5C,OAGF8B,EAAW/B,EAAEC,EAAI,mBACjB+B,EAAUhC,EAAEC,EAAI,kBAChBgC,EAAUjC,EAAEC,EAAI,kBAGhBA,EAAG6C,YAAcD,IAKZ5C,GAAO0C,IACRI,EAAOpB,UAlUXS,EAAWpC,EAAEC,EAAI,uBAEfmC,EAAWqH,SAASI,cAAc,SACzB5B,UAAUC,IAAI,kBACvBlE,GAAU,GAEZI,EACEhC,EAAQ,oBACYW,EAAOjC,aAAY,aAAaiC,EAAOhC,YAAW,KAEpEiD,GACFjC,EAAS+H,YAAY1H,IA0TnBW,EAAOnB,WAvTW,WACtB,IAAIoC,GACJ3B,EAAcrC,EAAEC,EAAI,0BAElBoC,EAAcoH,SAASI,cAAc,SACzB5B,UAAUC,IAAI,qBAC1BlE,GAAU,GAGZI,EAAc/B,EAAW,sBACrB2B,GACF/B,EAAQ6H,YAAYzH,E,CA6SpB0H,GAEEhH,EAAO7B,cACyB,iBAAvB6B,EAAO7B,YAChBjB,EAAGgI,UAAUC,IAAG,wBAAyBnF,EAAO7B,aAEhDjB,EAAGgI,UAAUC,IAAI,yBAGjBlI,EAAE8B,EAAgB,2BACpB+C,EAAkB,CAAEM,aAAa,IAEnCb,EAAImF,SAAU,QAASN,GACvB7E,EAAI3B,EAAU,cAAewF,GAC7B7D,EAAI3B,EAAU,eAAgBwF,GAC9B7D,EAAI3B,EAAU,cAAegG,GAC7BrE,EAAI3B,EAAU,YAAa8F,GAC3BnE,EAAI3B,EAAU,eAAgBsG,GAC9B3E,EAAI3B,EAAU,YAAasG,GAC3B3E,EAAI3B,EAAU,qBAAsBsG,GAEhClG,EAAOnC,eACToH,IACAvB,MAwBG5D,CACT,C"}