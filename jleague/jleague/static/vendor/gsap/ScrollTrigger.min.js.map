{"version": 3, "file": "ScrollTrigger.min.js", "sources": ["../src/Observer.js", "../src/ScrollTrigger.js"], "sourcesContent": ["/*!\n * Observer 3.12.2\n * https://greensock.com\n *\n * @license Copyright 2008-2023, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _clamp, _win, _doc, _docEl, _body, _isTouch, _pointerType, ScrollTrigger, _root, _normalizer, _eventTypes, _context,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_startup = 1,\n\t_observers = [],\n\t_scrollers = [],\n\t_proxies = [],\n\t_getTime = Date.now,\n\t_bridge = (name, value) => value,\n\t_integrate = () => {\n\t\tlet core = ScrollTrigger.core,\n\t\t\tdata = core.bridge || {},\n\t\t\tscrollers = core._scrollers,\n\t\t\tproxies = core._proxies;\n\t\tscrollers.push(..._scrollers);\n\t\tproxies.push(..._proxies);\n\t\t_scrollers = scrollers;\n\t\t_proxies = proxies;\n\t\t_bridge = (name, value) => data[name](value);\n\t},\n\t_getProxyProp = (element, property) => ~_proxies.indexOf(element) && _proxies[_proxies.indexOf(element) + 1][property],\n\t_isViewport = el => !!~_root.indexOf(el),\n\t_addListener = (element, type, func, nonPassive, capture) => element.addEventListener(type, func, {passive: !nonPassive, capture: !!capture}),\n\t_removeListener = (element, type, func, capture) => element.removeEventListener(type, func, !!capture),\n\t_scrollLeft = \"scrollLeft\",\n\t_scrollTop = \"scrollTop\",\n\t_onScroll = () => (_normalizer && _normalizer.isPressed) || _scrollers.cache++,\n\t_scrollCacheFunc = (f, doNotCache) => {\n\t\tlet cachingFunc = value => { // since reading the scrollTop/scrollLeft/pageOffsetY/pageOffsetX can trigger a layout, this function allows us to cache the value so it only gets read fresh after a \"scroll\" event fires (or while we're refreshing because that can lengthen the page and alter the scroll position). when \"soft\" is true, that means don't actually set the scroll, but cache the new value instead (useful in ScrollSmoother)\n\t\t\tif (value || value === 0) {\n\t\t\t\t_startup && (_win.history.scrollRestoration = \"manual\"); // otherwise the new position will get overwritten by the browser onload.\n\t\t\t\tlet isNormalizing = _normalizer && _normalizer.isPressed;\n\t\t\t\tvalue = cachingFunc.v = Math.round(value) || (_normalizer && _normalizer.iOS ? 1 : 0); //TODO: iOS Bug: if you allow it to go to 0, Safari can start to report super strange (wildly inaccurate) touch positions!\n\t\t\t\tf(value);\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tisNormalizing && _bridge(\"ss\", value); // set scroll (notify ScrollTrigger so it can dispatch a \"scrollStart\" event if necessary\n\t\t\t} else if (doNotCache || _scrollers.cache !== cachingFunc.cacheID || _bridge(\"ref\")) {\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tcachingFunc.v = f();\n\t\t\t}\n\t\t\treturn cachingFunc.v + cachingFunc.offset;\n\t\t};\n\t\tcachingFunc.offset = 0;\n\t\treturn f && cachingFunc;\n\t},\n\t_horizontal = {s: _scrollLeft, p: \"left\", p2: \"Left\", os: \"right\", os2: \"Right\", d: \"width\", d2: \"Width\", a: \"x\", sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(value, _vertical.sc()) : _win.pageXOffset || _doc[_scrollLeft] || _docEl[_scrollLeft] || _body[_scrollLeft] || 0})},\n\t_vertical = {s: _scrollTop, p: \"top\", p2: \"Top\", os: \"bottom\", os2: \"Bottom\", d: \"height\", d2: \"Height\", a: \"y\", op: _horizontal, sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(_horizontal.sc(), value) : _win.pageYOffset || _doc[_scrollTop] || _docEl[_scrollTop] || _body[_scrollTop] || 0})},\n\t_getTarget = (t, self) => ((self && self._ctx && self._ctx.selector) || gsap.utils.toArray)(t)[0] || (typeof(t) === \"string\" && gsap.config().nullTargetWarn !== false ? console.warn(\"Element not found:\", t) : null),\n\n\t_getScrollFunc = (element, {s, sc}) => { // we store the scroller functions in an alternating sequenced Array like [element, verticalScrollFunc, horizontalScrollFunc, ...] so that we can minimize memory, maximize performance, and we also record the last position as a \".rec\" property in order to revert to that after refreshing to ensure things don't shift around.\n\t\t_isViewport(element) && (element = _doc.scrollingElement || _docEl);\n\t\tlet i = _scrollers.indexOf(element),\n\t\t\toffset = sc === _vertical.sc ? 1 : 2;\n\t\t!~i && (i = _scrollers.push(element) - 1);\n\t\t_scrollers[i + offset] || _addListener(element, \"scroll\", _onScroll); // clear the cache when a scroll occurs\n\t\tlet prev = _scrollers[i + offset],\n\t\t\tfunc = prev || (_scrollers[i + offset] = _scrollCacheFunc(_getProxyProp(element, s), true) || (_isViewport(element) ? sc : _scrollCacheFunc(function(value) { return arguments.length ? (element[s] = value) : element[s]; })));\n\t\tfunc.target = element;\n\t\tprev || (func.smooth = gsap.getProperty(element, \"scrollBehavior\") === \"smooth\"); // only set it the first time (don't reset every time a scrollFunc is requested because perhaps it happens during a refresh() when it's disabled in ScrollTrigger.\n\t\treturn func;\n\t},\n\t_getVelocityProp = (value, minTimeRefresh, useDelta) => {\n\t\tlet v1 = value,\n\t\t\tv2 = value,\n\t\t\tt1 = _getTime(),\n\t\t\tt2 = t1,\n\t\t\tmin = minTimeRefresh || 50,\n\t\t\tdropToZeroTime = Math.max(500, min * 3),\n\t\t\tupdate = (value, force) => {\n\t\t\t\tlet t = _getTime();\n\t\t\t\tif (force || t - t1 > min) {\n\t\t\t\t\tv2 = v1;\n\t\t\t\t\tv1 = value;\n\t\t\t\t\tt2 = t1;\n\t\t\t\t\tt1 = t;\n\t\t\t\t} else if (useDelta) {\n\t\t\t\t\tv1 += value;\n\t\t\t\t} else { // not totally necessary, but makes it a bit more accurate by adjusting the v1 value according to the new slope. This way we're not just ignoring the incoming data. Removing for now because it doesn't seem to make much practical difference and it's probably not worth the kb.\n\t\t\t\t\tv1 = v2 + (value - v2) / (t - t2) * (t1 - t2);\n\t\t\t\t}\n\t\t\t},\n\t\t\treset = () => { v2 = v1 = useDelta ? 0 : v1; t2 = t1 = 0; },\n\t\t\tgetVelocity = latestValue => {\n\t\t\t\tlet tOld = t2,\n\t\t\t\t\tvOld = v2,\n\t\t\t\t\tt = _getTime();\n\t\t\t\t(latestValue || latestValue === 0) && latestValue !== v1 && update(latestValue);\n\t\t\t\treturn (t1 === t2 || t - t2 > dropToZeroTime) ? 0 : (v1 + (useDelta ? vOld : -vOld)) / ((useDelta ? t : t1) - tOld) * 1000;\n\t\t\t};\n\t\treturn {update, reset, getVelocity};\n\t},\n\t_getEvent = (e, preventDefault) => {\n\t\tpreventDefault && !e._gsapAllow && e.preventDefault();\n\t\treturn e.changedTouches ? e.changedTouches[0] : e;\n\t},\n\t_getAbsoluteMax = a => {\n\t\tlet max = Math.max(...a),\n\t\t\tmin = Math.min(...a);\n\t\treturn Math.abs(max) >= Math.abs(min) ? max : min;\n\t},\n\t_setScrollTrigger = () => {\n\t\tScrollTrigger = gsap.core.globals().ScrollTrigger;\n\t\tScrollTrigger && ScrollTrigger.core && _integrate();\n\t},\n\t_initCore = core => {\n\t\tgsap = core || _getGSAP();\n\t\tif (gsap && typeof(document) !== \"undefined\" && document.body) {\n\t\t\t_win = window;\n\t\t\t_doc = document;\n\t\t\t_docEl = _doc.documentElement;\n\t\t\t_body = _doc.body;\n\t\t\t_root = [_win, _doc, _docEl, _body];\n\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t_context = gsap.core.context || function() {};\n\t\t\t_pointerType = \"onpointerenter\" in _body ? \"pointer\" : \"mouse\";\n\t\t\t// isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\t\t\t_isTouch = Observer.isTouch = _win.matchMedia && _win.matchMedia(\"(hover: none), (pointer: coarse)\").matches ? 1 : (\"ontouchstart\" in _win || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0) ? 2 : 0;\n\t\t\t_eventTypes = Observer.eventTypes = (\"ontouchstart\" in _docEl ? \"touchstart,touchmove,touchcancel,touchend\" : !(\"onpointerdown\" in _docEl) ? \"mousedown,mousemove,mouseup,mouseup\" : \"pointerdown,pointermove,pointercancel,pointerup\").split(\",\");\n\t\t\tsetTimeout(() => _startup = 0, 500);\n\t\t\t_setScrollTrigger();\n\t\t\t_coreInitted = 1;\n\t\t}\n\t\treturn _coreInitted;\n\t};\n\n_horizontal.op = _vertical;\n_scrollers.cache = 0;\n\nexport class Observer {\n\tconstructor(vars) {\n\t\tthis.init(vars);\n\t}\n\n\tinit(vars) {\n\t\t_coreInitted || _initCore(gsap) || console.warn(\"Please gsap.registerPlugin(Observer)\");\n\t\tScrollTrigger || _setScrollTrigger();\n\t\tlet {tolerance, dragMinimum, type, target, lineHeight, debounce, preventDefault, onStop, onStopDelay, ignore, wheelSpeed, event, onDragStart, onDragEnd, onDrag, onPress, onRelease, onRight, onLeft, onUp, onDown, onChangeX, onChangeY, onChange, onToggleX, onToggleY, onHover, onHoverEnd, onMove, ignoreCheck, isNormalizer, onGestureStart, onGestureEnd, onWheel, onEnable, onDisable, onClick, scrollSpeed, capture, allowClicks, lockAxis, onLockAxis} = vars;\n\t\tthis.target = target = _getTarget(target) || _docEl;\n\t\tthis.vars = vars;\n\t\tignore && (ignore = gsap.utils.toArray(ignore));\n\t\ttolerance = tolerance || 1e-9;\n\t\tdragMinimum = dragMinimum || 0;\n\t\twheelSpeed = wheelSpeed || 1;\n\t\tscrollSpeed = scrollSpeed || 1;\n\t\ttype = type || \"wheel,touch,pointer\";\n\t\tdebounce = debounce !== false;\n\t\tlineHeight || (lineHeight = parseFloat(_win.getComputedStyle(_body).lineHeight) || 22); // note: browser may report \"normal\", so default to 22.\n\t\tlet id, onStopDelayedCall, dragged, moved, wheeled, locked, axis,\n\t\t\tself = this,\n\t\t\tprevDeltaX = 0,\n\t\t\tprevDeltaY = 0,\n\t\t\tscrollFuncX = _getScrollFunc(target, _horizontal),\n\t\t\tscrollFuncY = _getScrollFunc(target, _vertical),\n\t\t\tscrollX = scrollFuncX(),\n\t\t\tscrollY = scrollFuncY(),\n\t\t\tlimitToTouch = ~type.indexOf(\"touch\") && !~type.indexOf(\"pointer\") && _eventTypes[0] === \"pointerdown\", // for devices that accommodate mouse events and touch events, we need to distinguish.\n\t\t\tisViewport = _isViewport(target),\n\t\t\townerDoc = target.ownerDocument || _doc,\n\t\t\tdeltaX = [0, 0, 0], // wheel, scroll, pointer/touch\n\t\t\tdeltaY = [0, 0, 0],\n\t\t\tonClickTime = 0,\n\t\t\tclickCapture = () => onClickTime = _getTime(),\n\t\t\t_ignoreCheck = (e, isPointerOrTouch) => (self.event = e) && (ignore && ~ignore.indexOf(e.target)) || (isPointerOrTouch && limitToTouch && e.pointerType !== \"touch\") || (ignoreCheck && ignoreCheck(e, isPointerOrTouch)),\n\t\t\tonStopFunc = () => {\n\t\t\t\tself._vx.reset();\n\t\t\t\tself._vy.reset();\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tonStop && onStop(self);\n\t\t\t},\n\t\t\tupdate = () => {\n\t\t\t\tlet dx = self.deltaX = _getAbsoluteMax(deltaX),\n\t\t\t\t\tdy = self.deltaY = _getAbsoluteMax(deltaY),\n\t\t\t\t\tchangedX = Math.abs(dx) >= tolerance,\n\t\t\t\t\tchangedY = Math.abs(dy) >= tolerance;\n\t\t\t\tonChange && (changedX || changedY) && onChange(self, dx, dy, deltaX, deltaY); // in ScrollTrigger.normalizeScroll(), we need to know if it was touch/pointer so we need access to the deltaX/deltaY Arrays before we clear them out.\n\t\t\t\tif (changedX) {\n\t\t\t\t\tonRight && self.deltaX > 0 && onRight(self);\n\t\t\t\t\tonLeft && self.deltaX < 0 && onLeft(self);\n\t\t\t\t\tonChangeX && onChangeX(self);\n\t\t\t\t\tonToggleX && ((self.deltaX < 0) !== (prevDeltaX < 0)) && onToggleX(self);\n\t\t\t\t\tprevDeltaX = self.deltaX;\n\t\t\t\t\tdeltaX[0] = deltaX[1] = deltaX[2] = 0\n\t\t\t\t}\n\t\t\t\tif (changedY) {\n\t\t\t\t\tonDown && self.deltaY > 0 && onDown(self);\n\t\t\t\t\tonUp && self.deltaY < 0 && onUp(self);\n\t\t\t\t\tonChangeY && onChangeY(self);\n\t\t\t\t\tonToggleY && ((self.deltaY < 0) !== (prevDeltaY < 0)) && onToggleY(self);\n\t\t\t\t\tprevDeltaY = self.deltaY;\n\t\t\t\t\tdeltaY[0] = deltaY[1] = deltaY[2] = 0\n\t\t\t\t}\n\t\t\t\tif (moved || dragged) {\n\t\t\t\t\tonMove && onMove(self);\n\t\t\t\t\tif (dragged) {\n\t\t\t\t\t\tonDrag(self);\n\t\t\t\t\t\tdragged = false;\n\t\t\t\t\t}\n\t\t\t\t\tmoved = false;\n\t\t\t\t}\n\t\t\t\tlocked && !(locked = false) && onLockAxis && onLockAxis(self);\n\t\t\t\tif (wheeled) {\n\t\t\t\t\tonWheel(self);\n\t\t\t\t\twheeled = false;\n\t\t\t\t}\n\t\t\t\tid = 0;\n\t\t\t},\n\t\t\tonDelta = (x, y, index) => {\n\t\t\t\tdeltaX[index] += x;\n\t\t\t\tdeltaY[index] += y;\n\t\t\t\tself._vx.update(x);\n\t\t\t\tself._vy.update(y);\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\tonTouchOrPointerDelta = (x, y) => {\n\t\t\t\tif (lockAxis && !axis) {\n\t\t\t\t\tself.axis = axis = Math.abs(x) > Math.abs(y) ? \"x\" : \"y\";\n\t\t\t\t\tlocked = true;\n\t\t\t\t}\n\t\t\t\tif (axis !== \"y\") {\n\t\t\t\t\tdeltaX[2] += x;\n\t\t\t\t\tself._vx.update(x, true); // update the velocity as frequently as possible instead of in the debounced function so that very quick touch-scrolls (flicks) feel natural. If it's the mouse/touch/pointer, force it so that we get snappy/accurate momentum scroll.\n\t\t\t\t}\n\t\t\t\tif (axis !== \"x\") {\n\t\t\t\t\tdeltaY[2] += y;\n\t\t\t\t\tself._vy.update(y, true);\n\t\t\t\t}\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\t_onDrag = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y,\n\t\t\t\t\tisDragging = self.isDragging;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tif (isDragging || Math.abs(self.startX - x) >= dragMinimum || Math.abs(self.startY - y) >= dragMinimum) {\n\t\t\t\t\tonDrag && (dragged = true);\n\t\t\t\t\tisDragging || (self.isDragging = true);\n\t\t\t\t\tonTouchOrPointerDelta(dx, dy);\n\t\t\t\t\tisDragging || onDragStart && onDragStart(self);\n\t\t\t\t}\n\t\t\t},\n\t\t\t_onPress = self.onPress = e => {\n\t\t\t\tif (_ignoreCheck(e, 1) || (e && e.button)) {return;}\n\t\t\t\tself.axis = axis = null;\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tself.isPressed = true;\n\t\t\t\te = _getEvent(e); // note: may need to preventDefault(?) Won't side-scroll on iOS Safari if we do, though.\n\t\t\t\tprevDeltaX = prevDeltaY = 0;\n\t\t\t\tself.startX = self.x = e.clientX;\n\t\t\t\tself.startY = self.y = e.clientY;\n\t\t\t\tself._vx.reset(); // otherwise the t2 may be stale if the user touches and flicks super fast and releases in less than 2 requestAnimationFrame ticks, causing velocity to be 0.\n\t\t\t\tself._vy.reset();\n\t\t\t\t_addListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, preventDefault, true);\n\t\t\t\tself.deltaX = self.deltaY = 0;\n\t\t\t\tonPress && onPress(self);\n\t\t\t},\n\t\t\t_onRelease = self.onRelease = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\tlet isTrackingDrag = !isNaN(self.y - self.startY),\n\t\t\t\t\twasDragging = self.isDragging && (Math.abs(self.x - self.startX) > 3 || Math.abs(self.y - self.startY) > 3), // some touch devices need some wiggle room in terms of sensing clicks - the finger may move a few pixels.\n\t\t\t\t\teventData = _getEvent(e);\n\t\t\t\tif (!wasDragging && isTrackingDrag) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\tif (preventDefault && allowClicks) {\n\t\t\t\t\t\tgsap.delayedCall(0.08, () => { // some browsers (like Firefox) won't trust script-generated clicks, so if the user tries to click on a video to play it, for example, it simply won't work. Since a regular \"click\" event will most likely be generated anyway (one that has its isTrusted flag set to true), we must slightly delay our script-generated click so that the \"real\"/trusted one is prioritized. Remember, when there are duplicate events in quick succession, we suppress all but the first one. Some browsers don't even trigger the \"real\" one at all, so our synthetic one is a safety valve that ensures that no matter what, a click event does get dispatched.\n\t\t\t\t\t\t\tif (_getTime() - onClickTime > 300 && !e.defaultPrevented) {\n\t\t\t\t\t\t\t\tif (e.target.click) { //some browsers (like mobile Safari) don't properly trigger the click event\n\t\t\t\t\t\t\t\t\te.target.click();\n\t\t\t\t\t\t\t\t} else if (ownerDoc.createEvent) {\n\t\t\t\t\t\t\t\t\tlet syntheticEvent = ownerDoc.createEvent(\"MouseEvents\");\n\t\t\t\t\t\t\t\t\tsyntheticEvent.initMouseEvent(\"click\", true, true, _win, 1, eventData.screenX, eventData.screenY, eventData.clientX, eventData.clientY, false, false, false, false, 0, null);\n\t\t\t\t\t\t\t\t\te.target.dispatchEvent(syntheticEvent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\t\t\tonStop && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t\tonDragEnd && wasDragging && onDragEnd(self);\n\t\t\t\tonRelease && onRelease(self, wasDragging);\n\t\t\t},\n\t\t\t_onGestureStart = e => e.touches && e.touches.length > 1 && (self.isGesturing = true) && onGestureStart(e, self.isDragging),\n\t\t\t_onGestureEnd = () => (self.isGesturing = false) || onGestureEnd(self),\n\t\t\tonScroll = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = scrollFuncX(),\n\t\t\t\t\ty = scrollFuncY();\n\t\t\t\tonDelta((x - scrollX) * scrollSpeed, (y - scrollY) * scrollSpeed, 1);\n\t\t\t\tscrollX = x;\n\t\t\t\tscrollY = y;\n\t\t\t\tonStop && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onWheel = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tonWheel && (wheeled = true);\n\t\t\t\tlet multiplier = (e.deltaMode === 1 ? lineHeight : e.deltaMode === 2 ? _win.innerHeight : 1) * wheelSpeed;\n\t\t\t\tonDelta(e.deltaX * multiplier, e.deltaY * multiplier, 0);\n\t\t\t\tonStop && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onMove = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tmoved = true;\n\t\t\t\t(dx || dy) && onTouchOrPointerDelta(dx, dy);\n\t\t\t},\n\t\t\t_onHover = e => {self.event = e; onHover(self);},\n\t\t\t_onHoverEnd = e => {self.event = e; onHoverEnd(self);},\n\t\t\t_onClick = e => _ignoreCheck(e) || (_getEvent(e, preventDefault) && onClick(self));\n\n\t\tonStopDelayedCall = self._dc = gsap.delayedCall(onStopDelay || 0.25, onStopFunc).pause();\n\n\t\tself.deltaX = self.deltaY = 0;\n\t\tself._vx = _getVelocityProp(0, 50, true);\n\t\tself._vy = _getVelocityProp(0, 50, true);\n\t\tself.scrollX = scrollFuncX;\n\t\tself.scrollY = scrollFuncY;\n\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\t_context(this);\n\t\tself.enable = e => {\n\t\t\tif (!self.isEnabled) {\n\t\t\t\t_addListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\ttype.indexOf(\"scroll\") >= 0 && _addListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, preventDefault, capture);\n\t\t\t\ttype.indexOf(\"wheel\") >= 0 && _addListener(target, \"wheel\", _onWheel, preventDefault, capture);\n\t\t\t\tif ((type.indexOf(\"touch\") >= 0 && _isTouch) || type.indexOf(\"pointer\") >= 0) {\n\t\t\t\t\t_addListener(target, _eventTypes[0], _onPress, preventDefault, capture);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t\tallowClicks && _addListener(target, \"click\", clickCapture, false, true);\n\t\t\t\t\tonClick && _addListener(target, \"click\", _onClick);\n\t\t\t\t\tonGestureStart && _addListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t\tonGestureEnd && _addListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t\tonHover && _addListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t\tonHoverEnd && _addListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t\tonMove && _addListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\t}\n\t\t\t\tself.isEnabled = true;\n\t\t\t\te && e.type && _onPress(e);\n\t\t\t\tonEnable && onEnable(self);\n\t\t\t}\n\t\t\treturn self;\n\t\t};\n\t\tself.disable = () => {\n\t\t\tif (self.isEnabled) {\n\t\t\t\t// only remove the _onScroll listener if there aren't any others that rely on the functionality.\n\t\t\t\t_observers.filter(o => o !== self && _isViewport(o.target)).length || _removeListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\tif (self.isPressed) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\t}\n\t\t\t\t_removeListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, capture);\n\t\t\t\t_removeListener(target, \"wheel\", _onWheel, capture);\n\t\t\t\t_removeListener(target, _eventTypes[0], _onPress, capture);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t_removeListener(target, \"click\", clickCapture, true);\n\t\t\t\t_removeListener(target, \"click\", _onClick);\n\t\t\t\t_removeListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t_removeListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t_removeListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\tself.isEnabled = self.isPressed = self.isDragging = false;\n\t\t\t\tonDisable && onDisable(self);\n\t\t\t}\n\t\t};\n\n\t\tself.kill = self.revert = () => {\n\t\t\tself.disable();\n\t\t\tlet i = _observers.indexOf(self);\n\t\t\ti >= 0 && _observers.splice(i, 1);\n\t\t\t_normalizer === self && (_normalizer = 0);\n\t\t}\n\n\t\t_observers.push(self);\n\t\tisNormalizer && _isViewport(target) && (_normalizer = self);\n\n\t\tself.enable(event);\n\t}\n\n\tget velocityX() {\n\t\treturn this._vx.getVelocity();\n\t}\n\tget velocityY() {\n\t\treturn this._vy.getVelocity();\n\t}\n\n}\n\nObserver.version = \"3.12.2\";\nObserver.create = vars => new Observer(vars);\nObserver.register = _initCore;\nObserver.getAll = () => _observers.slice();\nObserver.getById = id => _observers.filter(o => o.vars.id === id)[0];\n\n_getGSAP() && gsap.registerPlugin(Observer);\n\nexport { Observer as default, _isViewport, _scrollers, _getScrollFunc, _getProxyProp, _proxies, _getVelocityProp, _vertical, _horizontal, _getTarget };", "/*!\n * ScrollTrigger 3.12.2\n * https://greensock.com\n *\n * @license Copyright 2008-2023, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { Observer, _getTarget, _vertical, _horizontal, _scrollers, _proxies, _getScrollFunc, _getProxyProp, _getVelocityProp } from \"./Observer.js\";\n\nlet gsap, _coreInitted, _win, _doc, _docEl, _body, _root, _resizeDelay, _toArray, _clamp, _time2, _syncInterval, _refreshing, _pointerIsDown, _transformProp, _i, _prevWidth, _prevHeight, _autoRefresh, _sort, _suppressOverwrites, _ignoreResize, _normalizer, _ignoreMobileResize, _baseScreenHeight, _baseScreenWidth, _fixIOSBug, _context, _scrollRestoration, _div100vh, _100vh,\n\t_limitCallbacks, // if true, we'll only trigger callbacks if the active state toggles, so if you scroll immediately past both the start and end positions of a ScrollTrigger (thus inactive to inactive), neither its onEnter nor onLeave will be called. This is useful during startup.\n\t_startup = 1,\n\t_getTime = Date.now,\n\t_time1 = _getTime(),\n\t_lastScrollTime = 0,\n\t_enabled = 0,\n\t_parseClamp = (value, type, self) => {\n\t\tlet clamp = (_isString(value) && (value.substr(0, 6) === \"clamp(\" || value.indexOf(\"max\") > -1));\n\t\tself[\"_\" + type + \"Clamp\"] = clamp;\n\t\treturn clamp ? value.substr(6, value.length - 7) : value;\n\t},\n\t_keepClamp = (value, clamp) => clamp && (!_isString(value) || value.substr(0, 6) !== \"clamp(\") ? \"clamp(\" + value + \")\" : value,\n\t_rafBugFix = () => _enabled && requestAnimationFrame(_rafBugFix), // in some browsers (like Firefox), screen repaints weren't consistent unless we had SOMETHING queued up in requestAnimationFrame()! So this just creates a super simple loop to keep it alive and smooth out repaints.\n\t_pointerDownHandler = () => _pointerIsDown = 1,\n\t_pointerUpHandler = () => _pointerIsDown = 0,\n\t_passThrough = v => v,\n\t_round = value => Math.round(value * 100000) / 100000 || 0,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_isViewport = e => !!~_root.indexOf(e),\n\t_getViewportDimension = dimensionProperty => (dimensionProperty === \"Height\" ? _100vh : _win[\"inner\" + dimensionProperty]) || _docEl[\"client\" + dimensionProperty] || _body[\"client\" + dimensionProperty],\n\t_getBoundsFunc = element => _getProxyProp(element, \"getBoundingClientRect\") || (_isViewport(element) ? () => {_winOffsets.width = _win.innerWidth; _winOffsets.height = _100vh; return _winOffsets;} : () => _getBounds(element)),\n\t_getSizeFunc = (scroller, isViewport, {d, d2, a}) => (a = _getProxyProp(scroller, \"getBoundingClientRect\")) ? () => a()[d] : () => (isViewport ? _getViewportDimension(d2) : scroller[\"client\" + d2]) || 0,\n\t_getOffsetsFunc = (element, isViewport) => !isViewport || ~_proxies.indexOf(element) ? _getBoundsFunc(element) : () => _winOffsets,\n\t_maxScroll = (element, {s, d2, d, a}) => Math.max(0, (s = \"scroll\" + d2) && (a = _getProxyProp(element, s)) ? a() - _getBoundsFunc(element)()[d] : _isViewport(element) ? (_docEl[s] || _body[s]) - _getViewportDimension(d2) : element[s] - element[\"offset\" + d2]),\n\t_iterateAutoRefresh = (func, events) => {\n\t\tfor (let i = 0; i < _autoRefresh.length; i += 3) {\n\t\t\t(!events || ~events.indexOf(_autoRefresh[i+1])) && func(_autoRefresh[i], _autoRefresh[i+1], _autoRefresh[i+2]);\n\t\t}\n\t},\n\t_isString = value => typeof(value) === \"string\",\n\t_isFunction = value => typeof(value) === \"function\",\n\t_isNumber = value => typeof(value) === \"number\",\n\t_isObject = value => typeof(value) === \"object\",\n\t_endAnimation = (animation, reversed, pause) => animation && animation.progress(reversed ? 0 : 1) && pause && animation.pause(),\n\t_callback = (self, func) => {\n\t\tif (self.enabled) {\n\t\t\tlet result = func(self);\n\t\t\tresult && result.totalTime && (self.callbackAnimation = result);\n\t\t}\n\t},\n\t_abs = Math.abs,\n\t_left = \"left\",\n\t_top = \"top\",\n\t_right = \"right\",\n\t_bottom = \"bottom\",\n\t_width = \"width\",\n\t_height = \"height\",\n\t_Right = \"Right\",\n\t_Left = \"Left\",\n\t_Top = \"Top\",\n\t_Bottom = \"Bottom\",\n\t_padding = \"padding\",\n\t_margin = \"margin\",\n\t_Width = \"Width\",\n\t_Height = \"Height\",\n\t_px = \"px\",\n\t_getComputedStyle = element => _win.getComputedStyle(element),\n\t_makePositionable = element => { // if the element already has position: absolute or fixed, leave that, otherwise make it position: relative\n\t\tlet position = _getComputedStyle(element).position;\n\t\telement.style.position = (position === \"absolute\" || position === \"fixed\") ? position : \"relative\";\n\t},\n\t_setDefaults = (obj, defaults) => {\n\t\tfor (let p in defaults) {\n\t\t\t(p in obj) || (obj[p] = defaults[p]);\n\t\t}\n\t\treturn obj;\n\t},\n\t_getBounds = (element, withoutTransforms) => {\n\t\tlet tween = withoutTransforms && _getComputedStyle(element)[_transformProp] !== \"matrix(1, 0, 0, 1, 0, 0)\" && gsap.to(element, {x: 0, y: 0, xPercent: 0, yPercent: 0, rotation: 0, rotationX: 0, rotationY: 0, scale: 1, skewX: 0, skewY: 0}).progress(1),\n\t\t\tbounds = element.getBoundingClientRect();\n\t\ttween && tween.progress(0).kill();\n\t\treturn bounds;\n\t},\n\t_getSize = (element, {d2}) => element[\"offset\" + d2] || element[\"client\" + d2] || 0,\n\t_getLabelRatioArray = timeline => {\n\t\tlet a = [],\n\t\t\tlabels = timeline.labels,\n\t\t\tduration = timeline.duration(),\n\t\t\tp;\n\t\tfor (p in labels) {\n\t\t\ta.push(labels[p] / duration);\n\t\t}\n\t\treturn a;\n\t},\n\t_getClosestLabel = animation => value => gsap.utils.snap(_getLabelRatioArray(animation), value),\n\t_snapDirectional = snapIncrementOrArray => {\n\t\tlet snap = gsap.utils.snap(snapIncrementOrArray),\n\t\t\ta = Array.isArray(snapIncrementOrArray) && snapIncrementOrArray.slice(0).sort((a, b) => a - b);\n\t\treturn a ? (value, direction, threshold= 1e-3) => {\n\t\t\tlet i;\n\t\t\tif (!direction) {\n\t\t\t\treturn snap(value);\n\t\t\t}\n\t\t\tif (direction > 0) {\n\t\t\t\tvalue -= threshold; // to avoid rounding errors. If we're too strict, it might snap forward, then immediately again, and again.\n\t\t\t\tfor (i = 0; i < a.length; i++) {\n\t\t\t\t\tif (a[i] >= value) {\n\t\t\t\t\t\treturn a[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn a[i-1];\n\t\t\t} else {\n\t\t\t\ti = a.length;\n\t\t\t\tvalue += threshold;\n\t\t\t\twhile (i--) {\n\t\t\t\t\tif (a[i] <= value) {\n\t\t\t\t\t\treturn a[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn a[0];\n\t\t} : (value, direction, threshold= 1e-3) => {\n\t\t\tlet snapped = snap(value);\n\t\t\treturn !direction || Math.abs(snapped - value) < threshold || ((snapped - value < 0) === direction < 0) ? snapped : snap(direction < 0 ? value - snapIncrementOrArray : value + snapIncrementOrArray);\n\t\t};\n\t},\n\t_getLabelAtDirection = timeline => (value, st) => _snapDirectional(_getLabelRatioArray(timeline))(value, st.direction),\n\t_multiListener = (func, element, types, callback) => types.split(\",\").forEach(type => func(element, type, callback)),\n\t_addListener = (element, type, func, nonPassive, capture) => element.addEventListener(type, func, {passive: !nonPassive, capture: !!capture}),\n\t_removeListener = (element, type, func, capture) => element.removeEventListener(type, func, !!capture),\n\t_wheelListener = (func, el, scrollFunc) => {\n\t\tscrollFunc = scrollFunc && scrollFunc.wheelHandler\n\t\tif (scrollFunc) {\n\t\t\tfunc(el, \"wheel\", scrollFunc);\n\t\t\tfunc(el, \"touchmove\", scrollFunc);\n\t\t}\n\t},\n\t_markerDefaults = {startColor: \"green\", endColor: \"red\", indent: 0, fontSize: \"16px\", fontWeight:\"normal\"},\n\t_defaults = {toggleActions: \"play\", anticipatePin: 0},\n\t_keywords = {top: 0, left: 0, center: 0.5, bottom: 1, right: 1},\n\t_offsetToPx = (value, size) => {\n\t\tif (_isString(value)) {\n\t\t\tlet eqIndex = value.indexOf(\"=\"),\n\t\t\t\trelative = ~eqIndex ? +(value.charAt(eqIndex-1) + 1) * parseFloat(value.substr(eqIndex + 1)) : 0;\n\t\t\tif (~eqIndex) {\n\t\t\t\t(value.indexOf(\"%\") > eqIndex) && (relative *= size / 100);\n\t\t\t\tvalue = value.substr(0, eqIndex-1);\n\t\t\t}\n\t\t\tvalue = relative + ((value in _keywords) ? _keywords[value] * size : ~value.indexOf(\"%\") ? parseFloat(value) * size / 100 : parseFloat(value) || 0);\n\t\t}\n\t\treturn value;\n\t},\n\t_createMarker = (type, name, container, direction, {startColor, endColor, fontSize, indent, fontWeight}, offset, matchWidthEl, containerAnimation) => {\n\t\tlet e = _doc.createElement(\"div\"),\n\t\t\tuseFixedPosition = _isViewport(container) || _getProxyProp(container, \"pinType\") === \"fixed\",\n\t\t\tisScroller = type.indexOf(\"scroller\") !== -1,\n\t\t\tparent = useFixedPosition ? _body : container,\n\t\t\tisStart = type.indexOf(\"start\") !== -1,\n\t\t\tcolor = isStart ? startColor : endColor,\n\t\t\tcss = \"border-color:\" + color + \";font-size:\" + fontSize + \";color:\" + color + \";font-weight:\" + fontWeight + \";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;\";\n\t\tcss += \"position:\" + ((isScroller || containerAnimation) && useFixedPosition ? \"fixed;\" : \"absolute;\");\n\t\t(isScroller || containerAnimation || !useFixedPosition) && (css += (direction === _vertical ? _right : _bottom) + \":\" + (offset + parseFloat(indent)) + \"px;\");\n\t\tmatchWidthEl && (css += \"box-sizing:border-box;text-align:left;width:\" + matchWidthEl.offsetWidth + \"px;\");\n\t\te._isStart = isStart;\n\t\te.setAttribute(\"class\", \"gsap-marker-\" + type + (name ? \" marker-\" + name : \"\"));\n\t\te.style.cssText = css;\n\t\te.innerText = name || name === 0 ? type + \"-\" + name : type;\n\t\tparent.children[0] ? parent.insertBefore(e, parent.children[0]) : parent.appendChild(e);\n\t\te._offset = e[\"offset\" + direction.op.d2];\n\t\t_positionMarker(e, 0, direction, isStart);\n\t\treturn e;\n\t},\n\t_positionMarker = (marker, start, direction, flipped) => {\n\t\tlet vars = {display: \"block\"},\n\t\t\tside = direction[flipped ? \"os2\" : \"p2\"],\n\t\t\toppositeSide = direction[flipped ? \"p2\" : \"os2\"];\n\t\tmarker._isFlipped = flipped;\n\t\tvars[direction.a + \"Percent\"] = flipped ? -100 : 0;\n\t\tvars[direction.a] = flipped ? \"1px\" : 0;\n\t\tvars[\"border\" + side + _Width] = 1;\n\t\tvars[\"border\" + oppositeSide + _Width] = 0;\n\t\tvars[direction.p] = start + \"px\";\n\t\tgsap.set(marker, vars);\n\t},\n\t_triggers = [],\n\t_ids = {},\n\t_rafID,\n\t_sync = () => _getTime() - _lastScrollTime > 34 && (_rafID || (_rafID = requestAnimationFrame(_updateAll))),\n\t_onScroll = () => { // previously, we tried to optimize performance by batching/deferring to the next requestAnimationFrame(), but discovered that Safari has a few bugs that make this unworkable (especially on iOS). See https://codepen.io/GreenSock/pen/16c435b12ef09c38125204818e7b45fc?editors=0010 and https://codepen.io/GreenSock/pen/JjOxYpQ/3dd65ccec5a60f1d862c355d84d14562?editors=0010 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503?editors=0010\n\t\tif (!_normalizer || !_normalizer.isPressed || _normalizer.startX > _body.clientWidth) { // if the user is dragging the scrollbar, allow it.\n\t\t\t_scrollers.cache++;\n\t\t\tif (_normalizer) {\n\t\t\t\t_rafID || (_rafID = requestAnimationFrame(_updateAll));\n\t\t\t} else {\n\t\t\t\t_updateAll(); // Safari in particular (on desktop) NEEDS the immediate update rather than waiting for a requestAnimationFrame() whereas iOS seems to benefit from waiting for the requestAnimationFrame() tick, at least when normalizing. See https://codepen.io/GreenSock/pen/qBYozqO?editors=0110\n\t\t\t}\n\t\t\t_lastScrollTime || _dispatch(\"scrollStart\");\n\t\t\t_lastScrollTime = _getTime();\n\t\t}\n\t},\n\t_setBaseDimensions = () => {\n\t\t_baseScreenWidth = _win.innerWidth;\n\t\t_baseScreenHeight = _win.innerHeight;\n\t},\n\t_onResize = () => {\n\t\t_scrollers.cache++;\n\t\t!_refreshing && !_ignoreResize && !_doc.fullscreenElement && !_doc.webkitFullscreenElement && (!_ignoreMobileResize || _baseScreenWidth !== _win.innerWidth || Math.abs(_win.innerHeight - _baseScreenHeight) > _win.innerHeight * 0.25) && _resizeDelay.restart(true);\n\t}, // ignore resizes triggered by refresh()\n\t_listeners = {},\n\t_emptyArray = [],\n\t_softRefresh = () => _removeListener(ScrollTrigger, \"scrollEnd\", _softRefresh) || _refreshAll(true),\n\t_dispatch = type => (_listeners[type] && _listeners[type].map(f => f())) || _emptyArray,\n\t_savedStyles = [], // when ScrollTrigger.saveStyles() is called, the inline styles are recorded in this Array in a sequential format like [element, cssText, gsCache, media]. This keeps it very memory-efficient and fast to iterate through.\n\t_revertRecorded = media => {\n\t\tfor (let i = 0; i < _savedStyles.length; i+=5) {\n\t\t\tif (!media || _savedStyles[i+4] && _savedStyles[i+4].query === media) {\n\t\t\t\t_savedStyles[i].style.cssText = _savedStyles[i+1];\n\t\t\t\t_savedStyles[i].getBBox && _savedStyles[i].setAttribute(\"transform\", _savedStyles[i+2] || \"\");\n\t\t\t\t_savedStyles[i+3].uncache = 1;\n\t\t\t}\n\t\t}\n\t},\n\t_revertAll = (kill, media) => {\n\t\tlet trigger;\n\t\tfor (_i = 0; _i < _triggers.length; _i++) {\n\t\t\ttrigger = _triggers[_i];\n\t\t\tif (trigger && (!media || trigger._ctx === media)) {\n\t\t\t\tif (kill) {\n\t\t\t\t\ttrigger.kill(1);\n\t\t\t\t} else {\n\t\t\t\t\ttrigger.revert(true, true);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tmedia && _revertRecorded(media);\n\t\tmedia || _dispatch(\"revert\");\n\t},\n\t_clearScrollMemory = (scrollRestoration, force) => { // zero-out all the recorded scroll positions. Don't use _triggers because if, for example, .matchMedia() is used to create some ScrollTriggers and then the user resizes and it removes ALL ScrollTriggers, and then go back to a size where there are ScrollTriggers, it would have kept the position(s) saved from the initial state.\n\t\t_scrollers.cache++;\n\t\t(force || !_refreshingAll) && _scrollers.forEach(obj => _isFunction(obj) && obj.cacheID++ && (obj.rec = 0));\n\t\t_isString(scrollRestoration) && (_win.history.scrollRestoration = _scrollRestoration = scrollRestoration);\n\t},\n\t_refreshingAll,\n\t_refreshID = 0,\n\t_queueRefreshID,\n\t_queueRefreshAll = () => { // we don't want to call _refreshAll() every time we create a new ScrollTrigger (for performance reasons) - it's better to batch them. Some frameworks dynamically load content and we can't rely on the window's \"load\" or \"DOMContentLoaded\" events to trigger it.\n\t\tif (_queueRefreshID !== _refreshID) {\n\t\t\tlet id = _queueRefreshID = _refreshID;\n\t\t\trequestAnimationFrame(() => id === _refreshID && _refreshAll(true));\n\t\t}\n\t},\n\t_refresh100vh = () => {\n\t\t_body.appendChild(_div100vh);\n\t\t_100vh = _div100vh.offsetHeight || _win.innerHeight;\n\t\t_body.removeChild(_div100vh);\n\t},\n\t_refreshAll = (force, skipRevert) => {\n\t\tif (_lastScrollTime && !force) {\n\t\t\t_addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\t\t\treturn;\n\t\t}\n\t\t_refresh100vh();\n\t\t_refreshingAll = ScrollTrigger.isRefreshing = true;\n\t\t_scrollers.forEach(obj => _isFunction(obj) && ++obj.cacheID && (obj.rec = obj())); // force the clearing of the cache because some browsers take a little while to dispatch the \"scroll\" event and the user may have changed the scroll position and then called ScrollTrigger.refresh() right away\n\t\tlet refreshInits = _dispatch(\"refreshInit\");\n\t\t_sort && ScrollTrigger.sort();\n\t\tskipRevert || _revertAll();\n\t\t_scrollers.forEach(obj => {\n\t\t\tif (_isFunction(obj)) {\n\t\t\t\tobj.smooth && (obj.target.style.scrollBehavior = \"auto\"); // smooth scrolling interferes\n\t\t\t\tobj(0);\n\t\t\t}\n\t\t});\n\t\t_triggers.slice(0).forEach(t => t.refresh()) // don't loop with _i because during a refresh() someone could call ScrollTrigger.update() which would iterate through _i resulting in a skip.\n\t\t_triggers.forEach((t, i) => { // nested pins (pinnedContainer) with pinSpacing may expand the container, so we must accommodate that here.\n\t\t\tif (t._subPinOffset && t.pin) {\n\t\t\t\tlet prop = t.vars.horizontal ? \"offsetWidth\" : \"offsetHeight\",\n\t\t\t\t\toriginal = t.pin[prop];\n\t\t\t\tt.revert(true, 1);\n\t\t\t\tt.adjustPinSpacing(t.pin[prop] - original);\n\t\t\t\tt.refresh();\n\t\t\t}\n\t\t});\n\t\t_triggers.forEach(t => { // the scroller's max scroll position may change after all the ScrollTriggers refreshed (like pinning could push it down), so we need to loop back and correct any with end: \"max\". Same for anything with a clamped end\n\t\t\tlet max = _maxScroll(t.scroller, t._dir);\n\t\t\t(t.vars.end === \"max\" || (t._endClamp && t.end > max)) && t.setPositions(t.start, Math.max(t.start+1, max), true);\n\t\t});\n\t\trefreshInits.forEach(result => result && result.render && result.render(-1)); // if the onRefreshInit() returns an animation (typically a gsap.set()), revert it. This makes it easy to put things in a certain spot before refreshing for measurement purposes, and then put things back.\n\t\t_scrollers.forEach(obj => {\n\t\t\tif (_isFunction(obj)) {\n\t\t\t\tobj.smooth && requestAnimationFrame(() => obj.target.style.scrollBehavior = \"smooth\");\n\t\t\t\tobj.rec && obj(obj.rec);\n\t\t\t}\n\t\t});\n\t\t_clearScrollMemory(_scrollRestoration, 1);\n\t\t_resizeDelay.pause();\n\t\t_refreshID++;\n\t\t_refreshingAll = 2;\n\t\t_updateAll(2);\n\t\t_triggers.forEach(t => _isFunction(t.vars.onRefresh) && t.vars.onRefresh(t));\n\t\t_refreshingAll = ScrollTrigger.isRefreshing = false;\n\t\t_dispatch(\"refresh\");\n\t},\n\t_lastScroll = 0,\n\t_direction = 1,\n\t_primary,\n\t_updateAll = (force) => {\n\t\tif (!_refreshingAll || force === 2) {\n\t\t\tScrollTrigger.isUpdating = true;\n\t\t\t_primary && _primary.update(0); // ScrollSmoother uses refreshPriority -9999 to become the primary that gets updated before all others because it affects the scroll position.\n\t\t\tlet l = _triggers.length,\n\t\t\t\ttime = _getTime(),\n\t\t\t\trecordVelocity = time - _time1 >= 50,\n\t\t\t\tscroll = l && _triggers[0].scroll();\n\t\t\t_direction = _lastScroll > scroll ? -1 : 1;\n\t\t\t_refreshingAll || (_lastScroll = scroll);\n\t\t\tif (recordVelocity) {\n\t\t\t\tif (_lastScrollTime && !_pointerIsDown && time - _lastScrollTime > 200) {\n\t\t\t\t\t_lastScrollTime = 0;\n\t\t\t\t\t_dispatch(\"scrollEnd\");\n\t\t\t\t}\n\t\t\t\t_time2 = _time1;\n\t\t\t\t_time1 = time;\n\t\t\t}\n\t\t\tif (_direction < 0) {\n\t\t\t\t_i = l;\n\t\t\t\twhile (_i-- > 0) {\n\t\t\t\t\t_triggers[_i] && _triggers[_i].update(0, recordVelocity);\n\t\t\t\t}\n\t\t\t\t_direction = 1;\n\t\t\t} else {\n\t\t\t\tfor (_i = 0; _i < l; _i++) {\n\t\t\t\t\t_triggers[_i] && _triggers[_i].update(0, recordVelocity);\n\t\t\t\t}\n\t\t\t}\n\t\t\tScrollTrigger.isUpdating = false;\n\t\t}\n\t\t_rafID = 0;\n\t},\n\t_propNamesToCopy = [_left, _top, _bottom, _right, _margin + _Bottom, _margin + _Right, _margin + _Top, _margin + _Left, \"display\", \"flexShrink\", \"float\", \"zIndex\", \"gridColumnStart\", \"gridColumnEnd\", \"gridRowStart\", \"gridRowEnd\", \"gridArea\", \"justifySelf\", \"alignSelf\", \"placeSelf\", \"order\"],\n\t_stateProps = _propNamesToCopy.concat([_width, _height, \"boxSizing\", \"max\" + _Width, \"max\" + _Height, \"position\", _margin, _padding, _padding + _Top, _padding + _Right, _padding + _Bottom, _padding + _Left]),\n\t_swapPinOut = (pin, spacer, state) => {\n\t\t_setState(state);\n\t\tlet cache = pin._gsap;\n\t\tif (cache.spacerIsNative) {\n\t\t\t_setState(cache.spacerState);\n\t\t} else if (pin._gsap.swappedIn) {\n\t\t\tlet parent = spacer.parentNode;\n\t\t\tif (parent) {\n\t\t\t\tparent.insertBefore(pin, spacer);\n\t\t\t\tparent.removeChild(spacer);\n\t\t\t}\n\t\t}\n\t\tpin._gsap.swappedIn = false;\n\t},\n\t_swapPinIn = (pin, spacer, cs, spacerState) => {\n\t\tif (!pin._gsap.swappedIn) {\n\t\t\tlet i = _propNamesToCopy.length,\n\t\t\t\tspacerStyle = spacer.style,\n\t\t\t\tpinStyle = pin.style,\n\t\t\t\tp;\n\t\t\twhile (i--) {\n\t\t\t\tp = _propNamesToCopy[i];\n\t\t\t\tspacerStyle[p] = cs[p];\n\t\t\t}\n\t\t\tspacerStyle.position = cs.position === \"absolute\" ? \"absolute\" : \"relative\";\n\t\t\t(cs.display === \"inline\") && (spacerStyle.display = \"inline-block\");\n\t\t\tpinStyle[_bottom] = pinStyle[_right] = \"auto\";\n\t\t\tspacerStyle.flexBasis = cs.flexBasis || \"auto\";\n\t\t\tspacerStyle.overflow = \"visible\";\n\t\t\tspacerStyle.boxSizing = \"border-box\";\n\t\t\tspacerStyle[_width] = _getSize(pin, _horizontal) + _px;\n\t\t\tspacerStyle[_height] = _getSize(pin, _vertical) + _px;\n\t\t\tspacerStyle[_padding] = pinStyle[_margin] = pinStyle[_top] = pinStyle[_left] = \"0\";\n\t\t\t_setState(spacerState);\n\t\t\tpinStyle[_width] = pinStyle[\"max\" + _Width] = cs[_width];\n\t\t\tpinStyle[_height] = pinStyle[\"max\" + _Height] = cs[_height];\n\t\t\tpinStyle[_padding] = cs[_padding];\n\t\t\tif (pin.parentNode !== spacer) {\n\t\t\t\tpin.parentNode.insertBefore(spacer, pin);\n\t\t\t\tspacer.appendChild(pin);\n\t\t\t}\n\t\t\tpin._gsap.swappedIn = true;\n\t\t}\n\t},\n\t_capsExp = /([A-Z])/g,\n\t_setState = state => {\n\t\tif (state) {\n\t\t\tlet style = state.t.style,\n\t\t\t\tl = state.length,\n\t\t\t\ti = 0,\n\t\t\t\tp, value;\n\t\t\t(state.t._gsap || gsap.core.getCache(state.t)).uncache = 1; // otherwise transforms may be off\n\t\t\tfor (; i < l; i +=2) {\n\t\t\t\tvalue = state[i+1];\n\t\t\t\tp = state[i];\n\t\t\t\tif (value) {\n\t\t\t\t\tstyle[p] = value;\n\t\t\t\t} else if (style[p]) {\n\t\t\t\t\tstyle.removeProperty(p.replace(_capsExp, \"-$1\").toLowerCase());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\t_getState = element => { // returns an Array with alternating values like [property, value, property, value] and a \"t\" property pointing to the target (element). Makes it fast and cheap.\n\t\tlet l = _stateProps.length,\n\t\t\tstyle = element.style,\n\t\t\tstate = [],\n\t\t\ti = 0;\n\t\tfor (; i < l; i++) {\n\t\t\tstate.push(_stateProps[i], style[_stateProps[i]]);\n\t\t}\n\t\tstate.t = element;\n\t\treturn state;\n\t},\n\t_copyState = (state, override, omitOffsets) => {\n\t\tlet result = [],\n\t\t\tl = state.length,\n\t\t\ti = omitOffsets ? 8 : 0, // skip top, left, right, bottom if omitOffsets is true\n\t\t\tp;\n\t\tfor (; i < l; i += 2) {\n\t\t\tp = state[i];\n\t\t\tresult.push(p, (p in override) ? override[p] : state[i+1]);\n\t\t}\n\t\tresult.t = state.t;\n\t\treturn result;\n\t},\n\t_winOffsets = {left:0, top:0},\n\t// // potential future feature (?) Allow users to calculate where a trigger hits (scroll position) like getScrollPosition(\"#id\", \"top bottom\")\n\t// _getScrollPosition = (trigger, position, {scroller, containerAnimation, horizontal}) => {\n\t// \tscroller = _getTarget(scroller || _win);\n\t// \tlet direction = horizontal ? _horizontal : _vertical,\n\t// \t\tisViewport = _isViewport(scroller);\n\t// \t_getSizeFunc(scroller, isViewport, direction);\n\t// \treturn _parsePosition(position, _getTarget(trigger), _getSizeFunc(scroller, isViewport, direction)(), direction, _getScrollFunc(scroller, direction)(), 0, 0, 0, _getOffsetsFunc(scroller, isViewport)(), isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0, 0, containerAnimation ? containerAnimation.duration() : _maxScroll(scroller), containerAnimation);\n\t// },\n\t_parsePosition = (value, trigger, scrollerSize, direction, scroll, marker, markerScroller, self, scrollerBounds, borderWidth, useFixedPosition, scrollerMax, containerAnimation, clampZeroProp) => {\n\t\t_isFunction(value) && (value = value(self));\n\t\tif (_isString(value) && value.substr(0,3) === \"max\") {\n\t\t\tvalue = scrollerMax + (value.charAt(4) === \"=\" ? _offsetToPx(\"0\" + value.substr(3), scrollerSize) : 0);\n\t\t}\n\t\tlet time = containerAnimation ? containerAnimation.time() : 0,\n\t\t\tp1, p2, element;\n\t\tcontainerAnimation && containerAnimation.seek(0);\n\t\tisNaN(value) || (value = +value); // convert a string number like \"45\" to an actual number\n\t\tif (!_isNumber(value)) {\n\t\t\t_isFunction(trigger) && (trigger = trigger(self));\n\t\t\tlet offsets = (value || \"0\").split(\" \"),\n\t\t\t\tbounds, localOffset, globalOffset, display;\n\t\t\telement = _getTarget(trigger, self) || _body;\n\t\t\tbounds = _getBounds(element) || {};\n\t\t\tif ((!bounds || (!bounds.left && !bounds.top)) && _getComputedStyle(element).display === \"none\") { // if display is \"none\", it won't report getBoundingClientRect() properly\n\t\t\t\tdisplay = element.style.display;\n\t\t\t\telement.style.display = \"block\";\n\t\t\t\tbounds = _getBounds(element);\n\t\t\t\tdisplay ? (element.style.display = display) : element.style.removeProperty(\"display\");\n\t\t\t}\n\t\t\tlocalOffset = _offsetToPx(offsets[0], bounds[direction.d]);\n\t\t\tglobalOffset = _offsetToPx(offsets[1] || \"0\", scrollerSize);\n\t\t\tvalue = bounds[direction.p] - scrollerBounds[direction.p] - borderWidth + localOffset + scroll - globalOffset;\n\t\t\tmarkerScroller && _positionMarker(markerScroller, globalOffset, direction, (scrollerSize - globalOffset < 20 || (markerScroller._isStart && globalOffset > 20)));\n\t\t\tscrollerSize -= scrollerSize - globalOffset; // adjust for the marker\n\t\t} else {\n\t\t\tcontainerAnimation && (value = gsap.utils.mapRange(containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, 0, scrollerMax, value));\n\t\t\tmarkerScroller && _positionMarker(markerScroller, scrollerSize, direction, true);\n\t\t}\n\t\tif (clampZeroProp) {\n\t\t\tself[clampZeroProp] = value || -0.001;\n\t\t\tvalue < 0 && (value = 0);\n\t\t}\n\t\tif (marker) {\n\t\t\tlet position = value + scrollerSize,\n\t\t\t\tisStart = marker._isStart;\n\t\t\tp1 = \"scroll\" + direction.d2;\n\t\t\t_positionMarker(marker, position, direction, (isStart && position > 20) || (!isStart && (useFixedPosition ? Math.max(_body[p1], _docEl[p1]) : marker.parentNode[p1]) <= position + 1));\n\t\t\tif (useFixedPosition) {\n\t\t\t\tscrollerBounds = _getBounds(markerScroller);\n\t\t\t\tuseFixedPosition && (marker.style[direction.op.p] = (scrollerBounds[direction.op.p] - direction.op.m - marker._offset) + _px);\n\t\t\t}\n\t\t}\n\t\tif (containerAnimation && element) {\n\t\t\tp1 = _getBounds(element);\n\t\t\tcontainerAnimation.seek(scrollerMax);\n\t\t\tp2 = _getBounds(element);\n\t\t\tcontainerAnimation._caScrollDist = p1[direction.p] - p2[direction.p];\n\t\t\tvalue = value / (containerAnimation._caScrollDist) * scrollerMax;\n\t\t}\n\t\tcontainerAnimation && containerAnimation.seek(time);\n\t\treturn containerAnimation ? value : Math.round(value);\n\t},\n\t_prefixExp = /(webkit|moz|length|cssText|inset)/i,\n\t_reparent = (element, parent, top, left) => {\n\t\tif (element.parentNode !== parent) {\n\t\t\tlet style = element.style,\n\t\t\t\tp, cs;\n\t\t\tif (parent === _body) {\n\t\t\t\telement._stOrig = style.cssText; // record original inline styles so we can revert them later\n\t\t\t\tcs = _getComputedStyle(element);\n\t\t\t\tfor (p in cs) { // must copy all relevant styles to ensure that nothing changes visually when we reparent to the <body>. Skip the vendor prefixed ones.\n\t\t\t\t\tif (!+p && !_prefixExp.test(p) && cs[p] && typeof style[p] === \"string\" && p !== \"0\") {\n\t\t\t\t\t\tstyle[p] = cs[p];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tstyle.top = top;\n\t\t\t\tstyle.left = left;\n\t\t\t} else {\n\t\t\t\tstyle.cssText = element._stOrig;\n\t\t\t}\n\t\t\tgsap.core.getCache(element).uncache = 1;\n\t\t\tparent.appendChild(element);\n\t\t}\n\t},\n\t_interruptionTracker = (getValueFunc, initialValue, onInterrupt) => {\n\t\tlet last1 = initialValue,\n\t\t\tlast2 = last1;\n\t\treturn value => {\n\t\t\tlet current = Math.round(getValueFunc()); // round because in some [very uncommon] Windows environments, scroll can get reported with decimals even though it was set without.\n\t\t\tif (current !== last1 && current !== last2 && Math.abs(current - last1) > 3 && Math.abs(current - last2) > 3) { // if the user scrolls, kill the tween. iOS Safari intermittently misreports the scroll position, it may be the most recently-set one or the one before that! When Safari is zoomed (CMD-+), it often misreports as 1 pixel off too! So if we set the scroll position to 125, for example, it'll actually report it as 124.\n\t\t\t\tvalue = current;\n\t\t\t\tonInterrupt && onInterrupt();\n\t\t\t}\n\t\t\tlast2 = last1;\n\t\t\tlast1 = value;\n\t\t\treturn value;\n\t\t};\n\t},\n\t_shiftMarker = (marker, direction, value) => {\n\t\tlet vars = {};\n\t\tvars[direction.p] = \"+=\" + value;\n\t\tgsap.set(marker, vars);\n\t},\n\t// _mergeAnimations = animations => {\n\t// \tlet tl = gsap.timeline({smoothChildTiming: true}).startTime(Math.min(...animations.map(a => a.globalTime(0))));\n\t// \tanimations.forEach(a => {let time = a.totalTime(); tl.add(a); a.totalTime(time); });\n\t// \ttl.smoothChildTiming = false;\n\t// \treturn tl;\n\t// },\n\n\t// returns a function that can be used to tween the scroll position in the direction provided, and when doing so it'll add a .tween property to the FUNCTION itself, and remove it when the tween completes or gets killed. This gives us a way to have multiple ScrollTriggers use a central function for any given scroller and see if there's a scroll tween running (which would affect if/how things get updated)\n\t_getTweenCreator = (scroller, direction) => {\n\t\tlet getScroll = _getScrollFunc(scroller, direction),\n\t\t\tprop = \"_scroll\" + direction.p2, // add a tweenable property to the scroller that's a getter/setter function, like _scrollTop or _scrollLeft. This way, if someone does gsap.killTweensOf(scroller) it'll kill the scroll tween.\n\t\t\tgetTween = (scrollTo, vars, initialValue, change1, change2) => {\n\t\t\t\tlet tween = getTween.tween,\n\t\t\t\t\tonComplete = vars.onComplete,\n\t\t\t\t\tmodifiers = {};\n\t\t\t\tinitialValue = initialValue || getScroll();\n\t\t\t\tlet checkForInterruption = _interruptionTracker(getScroll, initialValue, () => {\n\t\t\t\t\ttween.kill();\n\t\t\t\t\tgetTween.tween = 0;\n\t\t\t\t})\n\t\t\t\tchange2 = (change1 && change2) || 0; // if change1 is 0, we set that to the difference and ignore change2. Otherwise, there would be a compound effect.\n\t\t\t\tchange1 = change1 || (scrollTo - initialValue);\n\t\t\t\ttween && tween.kill();\n\t\t\t\tvars[prop] = scrollTo;\n\t\t\t\tvars.modifiers = modifiers;\n\t\t\t\tmodifiers[prop] = () => checkForInterruption(initialValue + change1 * tween.ratio + change2 * tween.ratio * tween.ratio);\n\t\t\t\tvars.onUpdate = () => {\n\t\t\t\t\t_scrollers.cache++;\n\t\t\t\t\t_updateAll();\n\t\t\t\t};\n\t\t\t\tvars.onComplete = () => {\n\t\t\t\t\tgetTween.tween = 0;\n\t\t\t\t\tonComplete && onComplete.call(tween);\n\t\t\t\t};\n\t\t\t\ttween = getTween.tween = gsap.to(scroller, vars);\n\t\t\t\treturn tween;\n\t\t\t};\n\t\tscroller[prop] = getScroll;\n\t\tgetScroll.wheelHandler = () => getTween.tween && getTween.tween.kill() && (getTween.tween = 0);\n\t\t_addListener(scroller, \"wheel\", getScroll.wheelHandler); // Windows machines handle mousewheel scrolling in chunks (like \"3 lines per scroll\") meaning the typical strategy for cancelling the scroll isn't as sensitive. It's much more likely to match one of the previous 2 scroll event positions. So we kill any snapping as soon as there's a wheel event.\n\t\tScrollTrigger.isTouch && _addListener(scroller, \"touchmove\", getScroll.wheelHandler);\n\t\treturn getTween;\n\t};\n\n\n\n\nexport class ScrollTrigger {\n\n\tconstructor(vars, animation) {\n\t\t_coreInitted || ScrollTrigger.register(gsap) || console.warn(\"Please gsap.registerPlugin(ScrollTrigger)\");\n\t\t_context(this);\n\t\tthis.init(vars, animation);\n\t}\n\n\tinit(vars, animation) {\n\t\tthis.progress = this.start = 0;\n\t\tthis.vars && this.kill(true, true); // in case it's being initted again\n\t\tif (!_enabled) {\n\t\t\tthis.update = this.refresh = this.kill = _passThrough;\n\t\t\treturn;\n\t\t}\n\t\tvars = _setDefaults((_isString(vars) || _isNumber(vars) || vars.nodeType) ? {trigger: vars} : vars, _defaults);\n\t\tlet {onUpdate, toggleClass, id, onToggle, onRefresh, scrub, trigger, pin, pinSpacing, invalidateOnRefresh, anticipatePin, onScrubComplete, onSnapComplete, once, snap, pinReparent, pinSpacer, containerAnimation, fastScrollEnd, preventOverlaps} = vars,\n\t\t\tdirection = vars.horizontal || (vars.containerAnimation && vars.horizontal !== false) ? _horizontal : _vertical,\n\t\t\tisToggle = !scrub && scrub !== 0,\n\t\t\tscroller = _getTarget(vars.scroller || _win),\n\t\t\tscrollerCache = gsap.core.getCache(scroller),\n\t\t\tisViewport = _isViewport(scroller),\n\t\t\tuseFixedPosition = (\"pinType\" in vars ? vars.pinType : _getProxyProp(scroller, \"pinType\") || (isViewport && \"fixed\")) === \"fixed\",\n\t\t\tcallbacks = [vars.onEnter, vars.onLeave, vars.onEnterBack, vars.onLeaveBack],\n\t\t\ttoggleActions = isToggle && vars.toggleActions.split(\" \"),\n\t\t\tmarkers = \"markers\" in vars ? vars.markers : _defaults.markers,\n\t\t\tborderWidth = isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0,\n\t\t\tself = this,\n\t\t\tonRefreshInit = vars.onRefreshInit && (() => vars.onRefreshInit(self)),\n\t\t\tgetScrollerSize = _getSizeFunc(scroller, isViewport, direction),\n\t\t\tgetScrollerOffsets = _getOffsetsFunc(scroller, isViewport),\n\t\t\tlastSnap = 0,\n\t\t\tlastRefresh = 0,\n\t\t\tprevProgress = 0,\n\t\t\tscrollFunc = _getScrollFunc(scroller, direction),\n\t\t\ttweenTo, pinCache, snapFunc, scroll1, scroll2, start, end, markerStart, markerEnd, markerStartTrigger, markerEndTrigger, markerVars, executingOnRefresh,\n\t\t\tchange, pinOriginalState, pinActiveState, pinState, spacer, offset, pinGetter, pinSetter, pinStart, pinChange, spacingStart, spacerState, markerStartSetter, pinMoves,\n\t\t\tmarkerEndSetter, cs, snap1, snap2, scrubTween, scrubSmooth, snapDurClamp, snapDelayedCall, prevScroll, prevAnimProgress, caMarkerSetter, customRevertReturn;\n\n\t\t// for the sake of efficiency, _startClamp/_endClamp serve like a truthy value indicating that clamping was enabled on the start/end, and ALSO store the actual pre-clamped numeric value. We tap into that in ScrollSmoother for speed effects. So for example, if start=\"clamp(top bottom)\" results in a start of -100 naturally, it would get clamped to 0 but -100 would be stored in _startClamp.\n\t\tself._startClamp = self._endClamp = false;\n\t\tself._dir = direction;\n\t\tanticipatePin *= 45;\n\t\tself.scroller = scroller;\n\t\tself.scroll = containerAnimation ? containerAnimation.time.bind(containerAnimation) : scrollFunc;\n\t\tscroll1 = scrollFunc();\n\t\tself.vars = vars;\n\t\tanimation = animation || vars.animation;\n\t\tif (\"refreshPriority\" in vars) {\n\t\t\t_sort = 1;\n\t\t\tvars.refreshPriority === -9999 && (_primary = self); // used by ScrollSmoother\n\t\t}\n\t\tscrollerCache.tweenScroll = scrollerCache.tweenScroll || {\n\t\t\ttop: _getTweenCreator(scroller, _vertical),\n\t\t\tleft: _getTweenCreator(scroller, _horizontal)\n\t\t};\n\t\tself.tweenTo = tweenTo = scrollerCache.tweenScroll[direction.p];\n\t\tself.scrubDuration = value => {\n\t\t\tscrubSmooth = _isNumber(value) && value;\n\t\t\tif (!scrubSmooth) {\n\t\t\t\tscrubTween && scrubTween.progress(1).kill();\n\t\t\t\tscrubTween = 0;\n\t\t\t} else {\n\t\t\t\tscrubTween ? scrubTween.duration(value) : (scrubTween = gsap.to(animation, {ease: \"expo\", totalProgress: \"+=0\", duration: scrubSmooth, paused: true, onComplete: () => onScrubComplete && onScrubComplete(self)}));\n\t\t\t}\n\t\t};\n\t\tif (animation) {\n\t\t\tanimation.vars.lazy = false;\n\t\t\t(animation._initted && !self.isReverted) || (animation.vars.immediateRender !== false && vars.immediateRender !== false && animation.duration() && animation.render(0, true, true)); // special case: if this ScrollTrigger gets re-initted, a from() tween with a stagger could get initted initially and then reverted on the re-init which means it'll need to get rendered again here to properly display things. Otherwise, See https://greensock.com/forums/topic/36777-scrollsmoother-splittext-nextjs/ and https://codepen.io/GreenSock/pen/eYPyPpd?editors=0010\n\t\t\tself.animation = animation.pause();\n\t\t\tanimation.scrollTrigger = self;\n\t\t\tself.scrubDuration(scrub);\n\t\t\tsnap1 = 0;\n\t\t\tid || (id = animation.vars.id);\n\t\t}\n\n\t\tif (snap) {\n\t\t\t// TODO: potential idea: use legitimate CSS scroll snapping by pushing invisible elements into the DOM that serve as snap positions, and toggle the document.scrollingElement.style.scrollSnapType onToggle. See https://codepen.io/GreenSock/pen/JjLrgWM for a quick proof of concept.\n\t\t\tif (!_isObject(snap) || snap.push) {\n\t\t\t\tsnap = {snapTo: snap};\n\t\t\t}\n\t\t\t(\"scrollBehavior\" in _body.style) && gsap.set(isViewport ? [_body, _docEl] : scroller, {scrollBehavior: \"auto\"}); // smooth scrolling doesn't work with snap.\n\t\t\t_scrollers.forEach(o => _isFunction(o) && o.target === (isViewport ? _doc.scrollingElement || _docEl : scroller) && (o.smooth = false)); // note: set smooth to false on both the vertical and horizontal scroll getters/setters\n\t\t\tsnapFunc = _isFunction(snap.snapTo) ? snap.snapTo : snap.snapTo === \"labels\" ? _getClosestLabel(animation) : snap.snapTo === \"labelsDirectional\" ? _getLabelAtDirection(animation) : snap.directional !== false ? (value, st) => _snapDirectional(snap.snapTo)(value, _getTime() - lastRefresh < 500 ? 0 : st.direction) : gsap.utils.snap(snap.snapTo);\n\t\t\tsnapDurClamp = snap.duration || {min: 0.1, max: 2};\n\t\t\tsnapDurClamp = _isObject(snapDurClamp) ? _clamp(snapDurClamp.min, snapDurClamp.max) : _clamp(snapDurClamp, snapDurClamp);\n\t\t\tsnapDelayedCall = gsap.delayedCall(snap.delay || (scrubSmooth / 2) || 0.1, () => {\n\t\t\t\tlet scroll = scrollFunc(),\n\t\t\t\t\trefreshedRecently = _getTime() - lastRefresh < 500,\n\t\t\t\t\ttween = tweenTo.tween;\n\t\t\t\tif ((refreshedRecently || Math.abs(self.getVelocity()) < 10) && !tween && !_pointerIsDown && lastSnap !== scroll) {\n\t\t\t\t\tlet progress = (scroll - start) / change, // don't use self.progress because this might run between the refresh() and when the scroll position updates and self.progress is set properly in the update() method.\n\t\t\t\t\t\ttotalProgress = animation && !isToggle ? animation.totalProgress() : progress,\n\t\t\t\t\t\tvelocity = refreshedRecently ? 0 : ((totalProgress - snap2) / (_getTime() - _time2) * 1000) || 0,\n\t\t\t\t\t\tchange1 = gsap.utils.clamp(-progress, 1 - progress, _abs(velocity / 2) * velocity / 0.185),\n\t\t\t\t\t\tnaturalEnd = progress + (snap.inertia === false ? 0 : change1),\n\t\t\t\t\t\tendValue = _clamp(0, 1, snapFunc(naturalEnd, self)),\n\t\t\t\t\t\tendScroll = Math.round(start + endValue * change),\n\t\t\t\t\t\t{ onStart, onInterrupt, onComplete } = snap;\n\t\t\t\t\tif (scroll <= end && scroll >= start && endScroll !== scroll) {\n\t\t\t\t\t\tif (tween && !tween._initted && tween.data <= _abs(endScroll - scroll)) { // there's an overlapping snap! So we must figure out which one is closer and let that tween live.\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (snap.inertia === false) {\n\t\t\t\t\t\t\tchange1 = endValue - progress;\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttweenTo(endScroll, {\n\t\t\t\t\t\t\tduration: snapDurClamp(_abs( (Math.max(_abs(naturalEnd - totalProgress), _abs(endValue - totalProgress)) * 0.185 / velocity / 0.05) || 0)),\n\t\t\t\t\t\t\tease: snap.ease || \"power3\",\n\t\t\t\t\t\t\tdata: _abs(endScroll - scroll), // record the distance so that if another snap tween occurs (conflict) we can prioritize the closest snap.\n\t\t\t\t\t\t\tonInterrupt: () => snapDelayedCall.restart(true) && onInterrupt && onInterrupt(self),\n\t\t\t\t\t\t\tonComplete: () => {\n\t\t\t\t\t\t\t\tself.update();\n\t\t\t\t\t\t\t\tlastSnap = scrollFunc();\n\t\t\t\t\t\t\t\tsnap1 = snap2 = animation && !isToggle ? animation.totalProgress() : self.progress;\n\t\t\t\t\t\t\t\tonSnapComplete && onSnapComplete(self);\n\t\t\t\t\t\t\t\tonComplete && onComplete(self);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, scroll, change1 * change, endScroll - scroll - change1 * change);\n\t\t\t\t\t\tonStart && onStart(self, tweenTo.tween);\n\t\t\t\t\t}\n\t\t\t\t} else if (self.isActive && lastSnap !== scroll) {\n\t\t\t\t\tsnapDelayedCall.restart(true);\n\t\t\t\t}\n\t\t\t}).pause();\n\t\t}\n\t\tid && (_ids[id] = self);\n\t\ttrigger = self.trigger = _getTarget(trigger || (pin !== true && pin));\n\n\t\t// if a trigger has some kind of scroll-related effect applied that could contaminate the \"y\" or \"x\" position (like a ScrollSmoother effect), we needed a way to temporarily revert it, so we use the stRevert property of the gsCache. It can return another function that we'll call at the end so it can return to its normal state.\n\t\tcustomRevertReturn = trigger && trigger._gsap && trigger._gsap.stRevert;\n\t\tcustomRevertReturn && (customRevertReturn = customRevertReturn(self));\n\n\t\tpin = pin === true ? trigger : _getTarget(pin);\n\t\t_isString(toggleClass) && (toggleClass = {targets: trigger, className: toggleClass});\n\t\tif (pin) {\n\t\t\t(pinSpacing === false || pinSpacing === _margin) || (pinSpacing = !pinSpacing && pin.parentNode && pin.parentNode.style && _getComputedStyle(pin.parentNode).display === \"flex\" ? false : _padding); // if the parent is display: flex, don't apply pinSpacing by default. We should check that pin.parentNode is an element (not shadow dom window)\n\t\t\tself.pin = pin;\n\t\t\tpinCache = gsap.core.getCache(pin);\n\t\t\tif (!pinCache.spacer) { // record the spacer and pinOriginalState on the cache in case someone tries pinning the same element with MULTIPLE ScrollTriggers - we don't want to have multiple spacers or record the \"original\" pin state after it has already been affected by another ScrollTrigger.\n\t\t\t\tif (pinSpacer) {\n\t\t\t\t\tpinSpacer = _getTarget(pinSpacer);\n\t\t\t\t\tpinSpacer && !pinSpacer.nodeType && (pinSpacer = pinSpacer.current || pinSpacer.nativeElement); // for React & Angular\n\t\t\t\t\tpinCache.spacerIsNative = !!pinSpacer;\n\t\t\t\t\tpinSpacer && (pinCache.spacerState = _getState(pinSpacer));\n\t\t\t\t}\n\t\t\t\tpinCache.spacer = spacer = pinSpacer || _doc.createElement(\"div\");\n\t\t\t\tspacer.classList.add(\"pin-spacer\");\n\t\t\t\tid && spacer.classList.add(\"pin-spacer-\" + id);\n\t\t\t\tpinCache.pinState = pinOriginalState = _getState(pin);\n\t\t\t} else {\n\t\t\t\tpinOriginalState = pinCache.pinState;\n\t\t\t}\n\t\t\tvars.force3D !== false && gsap.set(pin, {force3D: true});\n\t\t\tself.spacer = spacer = pinCache.spacer;\n\t\t\tcs = _getComputedStyle(pin);\n\t\t\tspacingStart = cs[pinSpacing + direction.os2];\n\t\t\tpinGetter = gsap.getProperty(pin);\n\t\t\tpinSetter = gsap.quickSetter(pin, direction.a, _px);\n\t\t\t// pin.firstChild && !_maxScroll(pin, direction) && (pin.style.overflow = \"hidden\"); // protects from collapsing margins, but can have unintended consequences as demonstrated here: https://codepen.io/GreenSock/pen/1e42c7a73bfa409d2cf1e184e7a4248d so it was removed in favor of just telling people to set up their CSS to avoid the collapsing margins (overflow: hidden | auto is just one option. Another is border-top: 1px solid transparent).\n\t\t\t_swapPinIn(pin, spacer, cs);\n\t\t\tpinState = _getState(pin);\n\t\t}\n\t\tif (markers) {\n\t\t\tmarkerVars = _isObject(markers) ? _setDefaults(markers, _markerDefaults) : _markerDefaults;\n\t\t\tmarkerStartTrigger = _createMarker(\"scroller-start\", id, scroller, direction, markerVars, 0);\n\t\t\tmarkerEndTrigger = _createMarker(\"scroller-end\", id, scroller, direction, markerVars, 0, markerStartTrigger);\n\t\t\toffset = markerStartTrigger[\"offset\" + direction.op.d2];\n\t\t\tlet content = _getTarget(_getProxyProp(scroller, \"content\") || scroller);\n\t\t\tmarkerStart = this.markerStart = _createMarker(\"start\", id, content, direction, markerVars, offset, 0, containerAnimation);\n\t\t\tmarkerEnd = this.markerEnd = _createMarker(\"end\", id, content, direction, markerVars, offset, 0, containerAnimation);\n\t\t\tcontainerAnimation && (caMarkerSetter = gsap.quickSetter([markerStart, markerEnd], direction.a, _px));\n\t\t\tif ((!useFixedPosition && !(_proxies.length && _getProxyProp(scroller, \"fixedMarkers\") === true))) {\n\t\t\t\t_makePositionable(isViewport ? _body : scroller);\n\t\t\t\tgsap.set([markerStartTrigger, markerEndTrigger], {force3D: true});\n\t\t\t\tmarkerStartSetter = gsap.quickSetter(markerStartTrigger, direction.a, _px);\n\t\t\t\tmarkerEndSetter = gsap.quickSetter(markerEndTrigger, direction.a, _px);\n\t\t\t}\n\t\t}\n\n\t\tif (containerAnimation) {\n\t\t\tlet oldOnUpdate = containerAnimation.vars.onUpdate,\n\t\t\t\toldParams = containerAnimation.vars.onUpdateParams;\n\t\t\tcontainerAnimation.eventCallback(\"onUpdate\", () => {\n\t\t\t\tself.update(0, 0, 1);\n\t\t\t\toldOnUpdate && oldOnUpdate.apply(containerAnimation, oldParams || []);\n\t\t\t});\n\t\t}\n\n\t\tself.previous = () => _triggers[_triggers.indexOf(self) - 1];\n\t\tself.next = () => _triggers[_triggers.indexOf(self) + 1];\n\n\t\tself.revert = (revert, temp) => {\n\t\t\tif (!temp) { return self.kill(true); } // for compatibility with gsap.context() and gsap.matchMedia() which call revert()\n\t\t\tlet r = revert !== false || !self.enabled,\n\t\t\t\tprevRefreshing = _refreshing;\n\t\t\tif (r !== self.isReverted) {\n\t\t\t\tif (r) {\n\t\t\t\t\tprevScroll = Math.max(scrollFunc(), self.scroll.rec || 0); // record the scroll so we can revert later (repositioning/pinning things can affect scroll position). In the static refresh() method, we first record all the scroll positions as a reference.\n\t\t\t\t\tprevProgress = self.progress;\n\t\t\t\t\tprevAnimProgress = animation && animation.progress();\n\t\t\t\t}\n\t\t\t\tmarkerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(m => m.style.display = r ? \"none\" : \"block\");\n\t\t\t\tif (r) {\n\t\t\t\t\t_refreshing = self;\n\t\t\t\t\tself.update(r); // make sure the pin is back in its original position so that all the measurements are correct. do this BEFORE swapping the pin out\n\t\t\t\t}\n\t\t\t\tif (pin && (!pinReparent || !self.isActive)) {\n\t\t\t\t\tif (r) {\n\t\t\t\t\t\t_swapPinOut(pin, spacer, pinOriginalState);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t_swapPinIn(pin, spacer, _getComputedStyle(pin), spacerState);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tr || self.update(r); // when we're restoring, the update should run AFTER swapping the pin into its pin-spacer.\n\t\t\t\t_refreshing = prevRefreshing; // restore. We set it to true during the update() so that things fire properly in there.\n\t\t\t\tself.isReverted = r;\n\t\t\t}\n\t\t}\n\n\t\tself.refresh = (soft, force, position, pinOffset) => { // position is typically only defined if it's coming from setPositions() - it's a way to skip the normal parsing. pinOffset is also only from setPositions() and is mostly related to fancy stuff we need to do in ScrollSmoother with effects\n\t\t\tif ((_refreshing || !self.enabled) && !force) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (pin && soft && _lastScrollTime) {\n\t\t\t\t_addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t!_refreshingAll && onRefreshInit && onRefreshInit(self);\n\t\t\t_refreshing = self;\n\t\t\tif (tweenTo.tween && !position) { // we skip this if a position is passed in because typically that's from .setPositions() and it's best to allow in-progress snapping to continue.\n\t\t\t\ttweenTo.tween.kill();\n\t\t\t\ttweenTo.tween = 0;\n\t\t\t}\n\t\t\tscrubTween && scrubTween.pause();\n\t\t\tinvalidateOnRefresh && animation && animation.revert({kill: false}).invalidate();\n\t\t\tself.isReverted || self.revert(true, true);\n\t\t\tself._subPinOffset = false; // we'll set this to true in the sub-pins if we find any\n\t\t\tlet size = getScrollerSize(),\n\t\t\t\tscrollerBounds = getScrollerOffsets(),\n\t\t\t\tmax = containerAnimation ? containerAnimation.duration() : _maxScroll(scroller, direction),\n\t\t\t\tisFirstRefresh = change <= 0.01,\n\t\t\t\toffset = 0,\n\t\t\t\totherPinOffset = pinOffset || 0,\n\t\t\t\tparsedEnd = _isObject(position) ? position.end : vars.end,\n\t\t\t\tparsedEndTrigger = vars.endTrigger || trigger,\n\t\t\t\tparsedStart = _isObject(position) ? position.start : (vars.start || (vars.start === 0 || !trigger ? 0 : (pin ? \"0 0\" : \"0 100%\"))),\n\t\t\t\tpinnedContainer = self.pinnedContainer = vars.pinnedContainer && _getTarget(vars.pinnedContainer, self),\n\t\t\t\ttriggerIndex = (trigger && Math.max(0, _triggers.indexOf(self))) || 0,\n\t\t\t\ti = triggerIndex,\n\t\t\t\tcs, bounds, scroll, isVertical, override, curTrigger, curPin, oppositeScroll, initted, revertedPins, forcedOverflow, markerStartOffset, markerEndOffset;\n\t\t\tif (markers && _isObject(position)) { // if we alter the start/end positions with .setPositions(), it generally feeds in absolute NUMBERS which don't convey information about where to line up the markers, so to keep it intuitive, we record how far the trigger positions shift after applying the new numbers and then offset by that much in the opposite direction. We do the same to the associated trigger markers too of course.\n\t\t\t\tmarkerStartOffset = gsap.getProperty(markerStartTrigger, direction.p);\n\t\t\t\tmarkerEndOffset = gsap.getProperty(markerEndTrigger, direction.p);\n\t\t\t}\n\t\t\twhile (i--) { // user might try to pin the same element more than once, so we must find any prior triggers with the same pin, revert them, and determine how long they're pinning so that we can offset things appropriately. Make sure we revert from last to first so that things \"rewind\" properly.\n\t\t\t\tcurTrigger = _triggers[i];\n\t\t\t\tcurTrigger.end || curTrigger.refresh(0, 1) || (_refreshing = self); // if it's a timeline-based trigger that hasn't been fully initialized yet because it's waiting for 1 tick, just force the refresh() here, otherwise if it contains a pin that's supposed to affect other ScrollTriggers further down the page, they won't be adjusted properly.\n\t\t\t\tcurPin = curTrigger.pin;\n\t\t\t\tif (curPin && (curPin === trigger || curPin === pin || curPin === pinnedContainer) && !curTrigger.isReverted) {\n\t\t\t\t\trevertedPins || (revertedPins = []);\n\t\t\t\t\trevertedPins.unshift(curTrigger); // we'll revert from first to last to make sure things reach their end state properly\n\t\t\t\t\tcurTrigger.revert(true, true);\n\t\t\t\t}\n\t\t\t\tif (curTrigger !== _triggers[i]) { // in case it got removed.\n\t\t\t\t\ttriggerIndex--;\n\t\t\t\t\ti--;\n\t\t\t\t}\n\t\t\t}\n\t\t\t_isFunction(parsedStart) && (parsedStart = parsedStart(self));\n\t\t\tparsedStart = _parseClamp(parsedStart, \"start\", self);\n\t\t\tstart = _parsePosition(parsedStart, trigger, size, direction, scrollFunc(), markerStart, markerStartTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._startClamp && \"_startClamp\") || (pin ? -0.001 : 0);\n\t\t\t_isFunction(parsedEnd) && (parsedEnd = parsedEnd(self));\n\t\t\tif (_isString(parsedEnd) && !parsedEnd.indexOf(\"+=\")) {\n\t\t\t\tif (~parsedEnd.indexOf(\" \")) {\n\t\t\t\t\tparsedEnd = (_isString(parsedStart) ? parsedStart.split(\" \")[0] : \"\") + parsedEnd;\n\t\t\t\t} else {\n\t\t\t\t\toffset = _offsetToPx(parsedEnd.substr(2), size);\n\t\t\t\t\tparsedEnd = _isString(parsedStart) ? parsedStart : (containerAnimation ? gsap.utils.mapRange(0, containerAnimation.duration(), containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, start) : start) + offset; // _parsePosition won't factor in the offset if the start is a number, so do it here.\n\t\t\t\t\tparsedEndTrigger = trigger;\n\t\t\t\t}\n\t\t\t}\n\t\t\tparsedEnd = _parseClamp(parsedEnd, \"end\", self);\n\t\t\tend = Math.max(start, _parsePosition(parsedEnd || (parsedEndTrigger ? \"100% 0\" : max), parsedEndTrigger, size, direction, scrollFunc() + offset, markerEnd, markerEndTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._endClamp && \"_endClamp\")) || -0.001;\n\n\t\t\toffset = 0;\n\t\t\ti = triggerIndex;\n\t\t\twhile (i--) {\n\t\t\t\tcurTrigger = _triggers[i];\n\t\t\t\tcurPin = curTrigger.pin;\n\t\t\t\tif (curPin && curTrigger.start - curTrigger._pinPush <= start && !containerAnimation && curTrigger.end > 0) {\n\t\t\t\t\tcs = curTrigger.end - (self._startClamp ? Math.max(0, curTrigger.start) : curTrigger.start);\n\t\t\t\t\tif (((curPin === trigger && curTrigger.start - curTrigger._pinPush < start) || curPin === pinnedContainer) && isNaN(parsedStart)) { // numeric start values shouldn't be offset at all - treat them as absolute\n\t\t\t\t\t\toffset += cs * (1 - curTrigger.progress);\n\t\t\t\t\t}\n\t\t\t\t\tcurPin === pin && (otherPinOffset += cs);\n\t\t\t\t}\n\t\t\t}\n\t\t\tstart += offset;\n\t\t\tend += offset;\n\t\t\tself._startClamp && (self._startClamp += offset);\n\n\t\t\tif (self._endClamp && !_refreshingAll) {\n\t\t\t\tself._endClamp = end || -0.001;\n\t\t\t\tend = Math.min(end, _maxScroll(scroller, direction));\n\t\t\t}\n\t\t\tchange = (end - start) || ((start -= 0.01) && 0.001);\n\n\t\t\tif (isFirstRefresh) { // on the very first refresh(), the prevProgress couldn't have been accurate yet because the start/end were never calculated, so we set it here. Before 3.11.5, it could lead to an inaccurate scroll position restoration with snapping.\n\t\t\t\tprevProgress = gsap.utils.clamp(0, 1, gsap.utils.normalize(start, end, prevScroll));\n\t\t\t}\n\t\t\tself._pinPush = otherPinOffset;\n\t\t\tif (markerStart && offset) { // offset the markers if necessary\n\t\t\t\tcs = {};\n\t\t\t\tcs[direction.a] = \"+=\" + offset;\n\t\t\t\tpinnedContainer && (cs[direction.p] = \"-=\" + scrollFunc());\n\t\t\t\tgsap.set([markerStart, markerEnd], cs);\n\t\t\t}\n\n\t\t\tif (pin) {\n\t\t\t\tcs = _getComputedStyle(pin);\n\t\t\t\tisVertical = direction === _vertical;\n\t\t\t\tscroll = scrollFunc(); // recalculate because the triggers can affect the scroll\n\t\t\t\tpinStart = parseFloat(pinGetter(direction.a)) + otherPinOffset;\n\t\t\t\tif (!max && end > 1) { // makes sure the scroller has a scrollbar, otherwise if something has width: 100%, for example, it would be too big (exclude the scrollbar). See https://greensock.com/forums/topic/25182-scrolltrigger-width-of-page-increase-where-markers-are-set-to-false/\n\t\t\t\t\tforcedOverflow = (isViewport ? (_doc.scrollingElement || _docEl) : scroller).style;\n\t\t\t\t\tforcedOverflow = {style: forcedOverflow, value: forcedOverflow[\"overflow\" + direction.a.toUpperCase()]};\n\t\t\t\t\tif (isViewport && _getComputedStyle(_body)[\"overflow\" + direction.a.toUpperCase()] !== \"scroll\") { // avoid an extra scrollbar if BOTH <html> and <body> have overflow set to \"scroll\"\n\t\t\t\t\t\tforcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = \"scroll\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t_swapPinIn(pin, spacer, cs);\n\t\t\t\tpinState = _getState(pin);\n\t\t\t\t// transforms will interfere with the top/left/right/bottom placement, so remove them temporarily. getBoundingClientRect() factors in transforms.\n\t\t\t\tbounds = _getBounds(pin, true);\n\t\t\t\toppositeScroll = useFixedPosition && _getScrollFunc(scroller, isVertical ? _horizontal : _vertical)();\n\t\t\t\tif (pinSpacing) {\n\t\t\t\t\tspacerState = [pinSpacing + direction.os2, change + otherPinOffset + _px];\n\t\t\t\t\tspacerState.t = spacer;\n\t\t\t\t\ti = (pinSpacing === _padding) ? _getSize(pin, direction) + change + otherPinOffset : 0;\n\t\t\t\t\ti && spacerState.push(direction.d, i + _px); // for box-sizing: border-box (must include padding).\n\t\t\t\t\t_setState(spacerState);\n\t\t\t\t\tif (pinnedContainer) { // in ScrollTrigger.refresh(), we need to re-evaluate the pinContainer's size because this pinSpacing may stretch it out, but we can't just add the exact distance because depending on layout, it may not push things down or it may only do so partially.\n\t\t\t\t\t\t_triggers.forEach(t => {\n\t\t\t\t\t\t\tif (t.pin === pinnedContainer && t.vars.pinSpacing !== false) {\n\t\t\t\t\t\t\t\tt._subPinOffset = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tuseFixedPosition && scrollFunc(prevScroll);\n\t\t\t\t}\n\t\t\t\tif (useFixedPosition) {\n\t\t\t\t\toverride = {\n\t\t\t\t\t\ttop: (bounds.top + (isVertical ? scroll - start : oppositeScroll)) + _px,\n\t\t\t\t\t\tleft: (bounds.left + (isVertical ? oppositeScroll : scroll - start)) + _px,\n\t\t\t\t\t\tboxSizing: \"border-box\",\n\t\t\t\t\t\tposition: \"fixed\"\n\t\t\t\t\t};\n\t\t\t\t\toverride[_width] = override[\"max\" + _Width] = Math.ceil(bounds.width) + _px;\n\t\t\t\t\toverride[_height] = override[\"max\" + _Height] = Math.ceil(bounds.height) + _px;\n\t\t\t\t\toverride[_margin] = override[_margin + _Top] = override[_margin + _Right] = override[_margin + _Bottom] = override[_margin + _Left] = \"0\";\n\t\t\t\t\toverride[_padding] = cs[_padding];\n\t\t\t\t\toverride[_padding + _Top] = cs[_padding + _Top];\n\t\t\t\t\toverride[_padding + _Right] = cs[_padding + _Right];\n\t\t\t\t\toverride[_padding + _Bottom] = cs[_padding + _Bottom];\n\t\t\t\t\toverride[_padding + _Left] = cs[_padding + _Left];\n\t\t\t\t\tpinActiveState = _copyState(pinOriginalState, override, pinReparent);\n\t\t\t\t\t_refreshingAll && scrollFunc(0);\n\t\t\t\t}\n\t\t\t\tif (animation) { // the animation might be affecting the transform, so we must jump to the end, check the value, and compensate accordingly. Otherwise, when it becomes unpinned, the pinSetter() will get set to a value that doesn't include whatever the animation did.\n\t\t\t\t\tinitted = animation._initted; // if not, we must invalidate() after this step, otherwise it could lock in starting values prematurely.\n\t\t\t\t\t_suppressOverwrites(1);\n\t\t\t\t\tanimation.render(animation.duration(), true, true);\n\t\t\t\t\tpinChange = pinGetter(direction.a) - pinStart + change + otherPinOffset;\n\t\t\t\t\tpinMoves = Math.abs(change - pinChange) > 1;\n\t\t\t\t\tuseFixedPosition && pinMoves && pinActiveState.splice(pinActiveState.length - 2, 2); // transform is the last property/value set in the state Array. Since the animation is controlling that, we should omit it.\n\t\t\t\t\tanimation.render(0, true, true);\n\t\t\t\t\tinitted || animation.invalidate(true);\n\t\t\t\t\tanimation.parent || animation.totalTime(animation.totalTime()); // if, for example, a toggleAction called play() and then refresh() happens and when we render(1) above, it would cause the animation to complete and get removed from its parent, so this makes sure it gets put back in.\n\t\t\t\t\t_suppressOverwrites(0);\n\t\t\t\t} else {\n\t\t\t\t\tpinChange = change\n\t\t\t\t}\n\t\t\t\tforcedOverflow && (forcedOverflow.value ? (forcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = forcedOverflow.value) : forcedOverflow.style.removeProperty(\"overflow-\" + direction.a));\n\t\t\t} else if (trigger && scrollFunc() && !containerAnimation) { // it may be INSIDE a pinned element, so walk up the tree and look for any elements with _pinOffset to compensate because anything with pinSpacing that's already scrolled would throw off the measurements in getBoundingClientRect()\n\t\t\t\tbounds = trigger.parentNode;\n\t\t\t\twhile (bounds && bounds !== _body) {\n\t\t\t\t\tif (bounds._pinOffset) {\n\t\t\t\t\t\tstart -= bounds._pinOffset;\n\t\t\t\t\t\tend -= bounds._pinOffset;\n\t\t\t\t\t}\n\t\t\t\t\tbounds = bounds.parentNode;\n\t\t\t\t}\n\t\t\t}\n\t\t\trevertedPins && revertedPins.forEach(t => t.revert(false, true));\n\t\t\tself.start = start;\n\t\t\tself.end = end;\n\t\t\tscroll1 = scroll2 = _refreshingAll ? prevScroll : scrollFunc(); // reset velocity\n\t\t\tif (!containerAnimation && !_refreshingAll) {\n\t\t\t\tscroll1 < prevScroll && scrollFunc(prevScroll);\n\t\t\t\tself.scroll.rec = 0;\n\t\t\t}\n\t\t\tself.revert(false, true);\n\t\t\tlastRefresh = _getTime();\n\t\t\tif (snapDelayedCall) {\n\t\t\t\tlastSnap = -1; // just so snapping gets re-enabled, clear out any recorded last value\n\t\t\t\t// self.isActive && scrollFunc(start + change * prevProgress); // previously this line was here to ensure that when snapping kicks in, it's from the previous progress but in some cases that's not desirable, like an all-page ScrollTrigger when new content gets added to the page, that'd totally change the progress.\n\t\t\t\tsnapDelayedCall.restart(true);\n\t\t\t}\n\t\t\t_refreshing = 0;\n\t\t\tanimation && isToggle && (animation._initted || prevAnimProgress) && animation.progress() !== prevAnimProgress && animation.progress(prevAnimProgress || 0, true).render(animation.time(), true, true); // must force a re-render because if saveStyles() was used on the target(s), the styles could have been wiped out during the refresh().\n\t\t\tif (isFirstRefresh || prevProgress !== self.progress || containerAnimation) { // ensures that the direction is set properly (when refreshing, progress is set back to 0 initially, then back again to wherever it needs to be) and that callbacks are triggered.\n\t\t\t\tanimation && !isToggle && animation.totalProgress(containerAnimation && start < -0.001 && !prevProgress ? gsap.utils.normalize(start, end, 0) : prevProgress, true); // to avoid issues where animation callbacks like onStart aren't triggered.\n\t\t\t\tself.progress = isFirstRefresh || ((scroll1 - start) / change === prevProgress) ? 0 : prevProgress;\n\t\t\t}\n\t\t\tpin && pinSpacing && (spacer._pinOffset = Math.round(self.progress * pinChange));\n\t\t\tscrubTween && scrubTween.invalidate();\n\n\t\t\tif (!isNaN(markerStartOffset)) { // numbers were passed in for the position which are absolute, so instead of just putting the markers at the very bottom of the viewport, we figure out how far they shifted down (it's safe to assume they were originally positioned in closer relation to the trigger element with values like \"top\", \"center\", a percentage or whatever, so we offset that much in the opposite direction to basically revert them to the relative position thy were at previously.\n\t\t\t\tmarkerStartOffset -= gsap.getProperty(markerStartTrigger, direction.p);\n\t\t\t\tmarkerEndOffset -= gsap.getProperty(markerEndTrigger, direction.p);\n\t\t\t\t_shiftMarker(markerStartTrigger, direction, markerStartOffset);\n\t\t\t\t_shiftMarker(markerStart, direction, markerStartOffset - (pinOffset || 0));\n\t\t\t\t_shiftMarker(markerEndTrigger, direction, markerEndOffset);\n\t\t\t\t_shiftMarker(markerEnd, direction, markerEndOffset - (pinOffset || 0));\n\t\t\t}\n\n\t\t\tisFirstRefresh && !_refreshingAll && self.update(); // edge case - when you reload a page when it's already scrolled down, some browsers fire a \"scroll\" event before DOMContentLoaded, triggering an updateAll(). If we don't update the self.progress as part of refresh(), then when it happens next, it may record prevProgress as 0 when it really shouldn't, potentially causing a callback in an animation to fire again.\n\n\t\t\tif (onRefresh && !_refreshingAll && !executingOnRefresh) { // when refreshing all, we do extra work to correct pinnedContainer sizes and ensure things don't exceed the maxScroll, so we should do all the refreshes at the end after all that work so that the start/end values are corrected.\n\t\t\t\texecutingOnRefresh = true;\n\t\t\t\tonRefresh(self);\n\t\t\t\texecutingOnRefresh = false;\n\t\t\t}\n\t\t};\n\n\t\tself.getVelocity = () => ((scrollFunc() - scroll2) / (_getTime() - _time2) * 1000) || 0;\n\n\t\tself.endAnimation = () => {\n\t\t\t_endAnimation(self.callbackAnimation);\n\t\t\tif (animation) {\n\t\t\t\tscrubTween ? scrubTween.progress(1) : (!animation.paused() ? _endAnimation(animation, animation.reversed()) : isToggle || _endAnimation(animation, self.direction < 0, 1));\n\t\t\t}\n\t\t};\n\n\t\tself.labelToScroll = label => animation && animation.labels && ((start || self.refresh() || start) + (animation.labels[label] / animation.duration()) * change) || 0;\n\n\t\tself.getTrailing = name => {\n\t\t\tlet i = _triggers.indexOf(self),\n\t\t\t\ta = self.direction > 0 ? _triggers.slice(0, i).reverse() : _triggers.slice(i+1);\n\t\t\treturn (_isString(name) ? a.filter(t => t.vars.preventOverlaps === name) : a).filter(t => self.direction > 0 ? t.end <= start : t.start >= end);\n\t\t};\n\n\n\t\tself.update = (reset, recordVelocity, forceFake) => {\n\t\t\tif (containerAnimation && !forceFake && !reset) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet scroll = _refreshingAll === true ? prevScroll : self.scroll(),\n\t\t\t\tp = reset ? 0 : (scroll - start) / change,\n\t\t\t\tclipped = p < 0 ? 0 : p > 1 ? 1 : p || 0,\n\t\t\t\tprevProgress = self.progress,\n\t\t\t\tisActive, wasActive, toggleState, action, stateChanged, toggled, isAtMax, isTakingAction;\n\t\t\tif (recordVelocity) {\n\t\t\t\tscroll2 = scroll1;\n\t\t\t\tscroll1 = containerAnimation ? scrollFunc() : scroll;\n\t\t\t\tif (snap) {\n\t\t\t\t\tsnap2 = snap1;\n\t\t\t\t\tsnap1 = animation && !isToggle ? animation.totalProgress() : clipped;\n\t\t\t\t}\n\t\t\t}\n\t\t\t// anticipate the pinning a few ticks ahead of time based on velocity to avoid a visual glitch due to the fact that most browsers do scrolling on a separate thread (not synced with requestAnimationFrame).\n\t\t\t(anticipatePin && !clipped && pin && !_refreshing && !_startup && _lastScrollTime && start < scroll + ((scroll - scroll2) / (_getTime() - _time2)) * anticipatePin) && (clipped = 0.0001);\n\t\t\tif (clipped !== prevProgress && self.enabled) {\n\t\t\t\tisActive = self.isActive = !!clipped && clipped < 1;\n\t\t\t\twasActive = !!prevProgress && prevProgress < 1;\n\t\t\t\ttoggled = isActive !== wasActive;\n\t\t\t\tstateChanged = toggled || !!clipped !== !!prevProgress; // could go from start all the way to end, thus it didn't toggle but it did change state in a sense (may need to fire a callback)\n\t\t\t\tself.direction = clipped > prevProgress ? 1 : -1;\n\t\t\t\tself.progress = clipped;\n\n\t\t\t\tif (stateChanged && !_refreshing) {\n\t\t\t\t\ttoggleState = clipped && !prevProgress ? 0 : clipped === 1 ? 1 : prevProgress === 1 ? 2 : 3; // 0 = enter, 1 = leave, 2 = enterBack, 3 = leaveBack (we prioritize the FIRST encounter, thus if you scroll really fast past the onEnter and onLeave in one tick, it'd prioritize onEnter.\n\t\t\t\t\tif (isToggle) {\n\t\t\t\t\t\taction = (!toggled && toggleActions[toggleState + 1] !== \"none\" && toggleActions[toggleState + 1]) || toggleActions[toggleState]; // if it didn't toggle, that means it shot right past and since we prioritize the \"enter\" action, we should switch to the \"leave\" in this case (but only if one is defined)\n\t\t\t\t\t\tisTakingAction = animation && (action === \"complete\" || action === \"reset\" || action in animation);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tpreventOverlaps && (toggled || isTakingAction) && (isTakingAction || scrub || !animation) && (_isFunction(preventOverlaps) ? preventOverlaps(self) : self.getTrailing(preventOverlaps).forEach(t => t.endAnimation()));\n\n\t\t\t\tif (!isToggle) {\n\t\t\t\t\tif (scrubTween && !_refreshing && !_startup) {\n\t\t\t\t\t\t(scrubTween._dp._time - scrubTween._start !== scrubTween._time) && scrubTween.render(scrubTween._dp._time - scrubTween._start); // if there's a scrub on both the container animation and this one (or a ScrollSmoother), the update order would cause this one not to have rendered yet, so it wouldn't make any progress before we .restart() it heading toward the new progress so it'd appear stuck thus we force a render here.\n\t\t\t\t\t\tif (scrubTween.resetTo) {\n\t\t\t\t\t\t\tscrubTween.resetTo(\"totalProgress\", clipped, animation._tTime / animation._tDur);\n\t\t\t\t\t\t} else { // legacy support (courtesy), before 3.10.0\n\t\t\t\t\t\t\tscrubTween.vars.totalProgress = clipped;\n\t\t\t\t\t\t\tscrubTween.invalidate().restart();\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (animation) {\n\t\t\t\t\t\tanimation.totalProgress(clipped, !!(_refreshing && (lastRefresh || reset)));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (pin) {\n\t\t\t\t\treset && pinSpacing && (spacer.style[pinSpacing + direction.os2] = spacingStart);\n\t\t\t\t\tif (!useFixedPosition) {\n\t\t\t\t\t\tpinSetter(_round(pinStart + pinChange * clipped));\n\t\t\t\t\t} else if (stateChanged) {\n\t\t\t\t\t\tisAtMax = !reset && clipped > prevProgress && end + 1 > scroll && scroll + 1 >= _maxScroll(scroller, direction); // if it's at the VERY end of the page, don't switch away from position: fixed because it's pointless and it could cause a brief flash when the user scrolls back up (when it gets pinned again)\n\t\t\t\t\t\tif (pinReparent) {\n\t\t\t\t\t\t\tif (!reset && (isActive || isAtMax)) {\n\t\t\t\t\t\t\t\tlet bounds = _getBounds(pin, true),\n\t\t\t\t\t\t\t\t\toffset = scroll - start;\n\t\t\t\t\t\t\t\t_reparent(pin, _body, (bounds.top + (direction === _vertical ? offset : 0)) + _px, (bounds.left + (direction === _vertical ? 0 : offset)) + _px);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t_reparent(pin, spacer);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t_setState(isActive || isAtMax ? pinActiveState : pinState);\n\t\t\t\t\t\t(pinMoves && clipped < 1 && isActive) || pinSetter(pinStart + (clipped === 1 && !isAtMax ? pinChange : 0));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tsnap && !tweenTo.tween && !_refreshing && !_startup && snapDelayedCall.restart(true);\n\t\t\t\ttoggleClass && (toggled || (once && clipped && (clipped < 1 || !_limitCallbacks))) && _toArray(toggleClass.targets).forEach(el => el.classList[isActive || once ? \"add\" : \"remove\"](toggleClass.className)); // classes could affect positioning, so do it even if reset or refreshing is true.\n\t\t\t\tonUpdate && !isToggle && !reset && onUpdate(self);\n\t\t\t\tif (stateChanged && !_refreshing) {\n\t\t\t\t\tif (isToggle) {\n\t\t\t\t\t\tif (isTakingAction) {\n\t\t\t\t\t\t\tif (action === \"complete\") {\n\t\t\t\t\t\t\t\tanimation.pause().totalProgress(1);\n\t\t\t\t\t\t\t} else if (action === \"reset\") {\n\t\t\t\t\t\t\t\tanimation.restart(true).pause();\n\t\t\t\t\t\t\t} else if (action === \"restart\") {\n\t\t\t\t\t\t\t\tanimation.restart(true);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tanimation[action]();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tonUpdate && onUpdate(self);\n\t\t\t\t\t}\n\t\t\t\t\tif (toggled || !_limitCallbacks) { // on startup, the page could be scrolled and we don't want to fire callbacks that didn't toggle. For example onEnter shouldn't fire if the ScrollTrigger isn't actually entered.\n\t\t\t\t\t\tonToggle && toggled && _callback(self, onToggle);\n\t\t\t\t\t\tcallbacks[toggleState] && _callback(self, callbacks[toggleState]);\n\t\t\t\t\t\tonce && (clipped === 1 ? self.kill(false, 1) : (callbacks[toggleState] = 0)); // a callback shouldn't be called again if once is true.\n\t\t\t\t\t\tif (!toggled) { // it's possible to go completely past, like from before the start to after the end (or vice-versa) in which case BOTH callbacks should be fired in that order\n\t\t\t\t\t\t\ttoggleState = clipped === 1 ? 1 : 3;\n\t\t\t\t\t\t\tcallbacks[toggleState] && _callback(self, callbacks[toggleState]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (fastScrollEnd && !isActive && Math.abs(self.getVelocity()) > (_isNumber(fastScrollEnd) ? fastScrollEnd : 2500)) {\n\t\t\t\t\t\t_endAnimation(self.callbackAnimation);\n\t\t\t\t\t\tscrubTween ? scrubTween.progress(1) : _endAnimation(animation, action === \"reverse\" ? 1 : !clipped, 1);\n\t\t\t\t\t}\n\t\t\t\t} else if (isToggle && onUpdate && !_refreshing) {\n\t\t\t\t\tonUpdate(self);\n\t\t\t\t}\n\t\t\t}\n\t\t\t// update absolutely-positioned markers (only if the scroller isn't the viewport)\n\t\t\tif (markerEndSetter) {\n\t\t\t\tlet n = containerAnimation ? scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0) : scroll;\n\t\t\t\tmarkerStartSetter(n + (markerStartTrigger._isFlipped ? 1 : 0));\n\t\t\t\tmarkerEndSetter(n);\n\t\t\t}\n\t\t\tcaMarkerSetter && caMarkerSetter(-scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0));\n\t\t};\n\n\t\tself.enable = (reset, refresh) => {\n\t\t\tif (!self.enabled) {\n\t\t\t\tself.enabled = true;\n\t\t\t\t_addListener(scroller, \"resize\", _onResize);\n\t\t\t\tisViewport || _addListener(scroller, \"scroll\", _onScroll);\n\t\t\t\tonRefreshInit && _addListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\t\t\t\tif (reset !== false) {\n\t\t\t\t\tself.progress = prevProgress = 0;\n\t\t\t\t\tscroll1 = scroll2 = lastSnap = scrollFunc();\n\t\t\t\t}\n\t\t\t\trefresh !== false && self.refresh();\n\t\t\t}\n\t\t};\n\n\t\tself.getTween = snap => snap && tweenTo ? tweenTo.tween : scrubTween;\n\n\t\tself.setPositions = (newStart, newEnd, keepClamp, pinOffset) => { // doesn't persist after refresh()! Intended to be a way to override values that were set during refresh(), like you could set it in onRefresh()\n\t\t\tif (containerAnimation) { // convert ratios into scroll positions. Remember, start/end values on ScrollTriggers that have a containerAnimation refer to the time (in seconds), NOT scroll positions.\n\t\t\t\tlet st = containerAnimation.scrollTrigger,\n\t\t\t\t\tduration = containerAnimation.duration(),\n\t\t\t\t\tchange = st.end - st.start;\n\t\t\t\tnewStart = st.start + change * newStart / duration;\n\t\t\t\tnewEnd = st.start + change * newEnd / duration;\n\t\t\t}\n\t\t\tself.refresh(false, false, {start: _keepClamp(newStart, keepClamp && !!self._startClamp), end: _keepClamp(newEnd, keepClamp && !!self._endClamp)}, pinOffset);\n\t\t\tself.update();\n\t\t};\n\n\t\tself.adjustPinSpacing = amount => {\n\t\t\tif (spacerState && amount) {\n\t\t\t\tlet i = spacerState.indexOf(direction.d) + 1;\n\t\t\t\tspacerState[i] = (parseFloat(spacerState[i]) + amount) + _px;\n\t\t\t\tspacerState[1] = (parseFloat(spacerState[1]) + amount) + _px;\n\t\t\t\t_setState(spacerState);\n\t\t\t}\n\t\t};\n\n\t\tself.disable = (reset, allowAnimation) => {\n\t\t\tif (self.enabled) {\n\t\t\t\treset !== false && self.revert(true, true);\n\t\t\t\tself.enabled = self.isActive = false;\n\t\t\t\tallowAnimation || (scrubTween && scrubTween.pause());\n\t\t\t\tprevScroll = 0;\n\t\t\t\tpinCache && (pinCache.uncache = 1);\n\t\t\t\tonRefreshInit && _removeListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\t\t\t\tif (snapDelayedCall) {\n\t\t\t\t\tsnapDelayedCall.pause();\n\t\t\t\t\ttweenTo.tween && tweenTo.tween.kill() && (tweenTo.tween = 0);\n\t\t\t\t}\n\t\t\t\tif (!isViewport) {\n\t\t\t\t\tlet i = _triggers.length;\n\t\t\t\t\twhile (i--) {\n\t\t\t\t\t\tif (_triggers[i].scroller === scroller && _triggers[i] !== self) {\n\t\t\t\t\t\t\treturn; //don't remove the listeners if there are still other triggers referencing it.\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t_removeListener(scroller, \"resize\", _onResize);\n\t\t\t\t\tisViewport || _removeListener(scroller, \"scroll\", _onScroll);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\tself.kill = (revert, allowAnimation) => {\n\t\t\tself.disable(revert, allowAnimation);\n\t\t\tscrubTween && !allowAnimation && scrubTween.kill();\n\t\t\tid && (delete _ids[id]);\n\t\t\tlet i = _triggers.indexOf(self);\n\t\t\ti >= 0 && _triggers.splice(i, 1);\n\t\t\ti === _i && _direction > 0 && _i--; // if we're in the middle of a refresh() or update(), splicing would cause skips in the index, so adjust...\n\n\t\t\t// if no other ScrollTrigger instances of the same scroller are found, wipe out any recorded scroll position. Otherwise, in a single page application, for example, it could maintain scroll position when it really shouldn't.\n\t\t\ti = 0;\n\t\t\t_triggers.forEach(t => t.scroller === self.scroller && (i = 1));\n\t\t\ti || _refreshingAll || (self.scroll.rec = 0);\n\n\t\t\tif (animation) {\n\t\t\t\tanimation.scrollTrigger = null;\n\t\t\t\trevert && animation.revert({kill: false});\n\t\t\t\tallowAnimation || animation.kill();\n\t\t\t}\n\t\t\tmarkerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(m => m.parentNode && m.parentNode.removeChild(m));\n\t\t\t_primary === self && (_primary = 0);\n\t\t\tif (pin) {\n\t\t\t\tpinCache && (pinCache.uncache = 1);\n\t\t\t\ti = 0;\n\t\t\t\t_triggers.forEach(t => t.pin === pin && i++);\n\t\t\t\ti || (pinCache.spacer = 0); // if there aren't any more ScrollTriggers with the same pin, remove the spacer, otherwise it could be contaminated with old/stale values if the user re-creates a ScrollTrigger for the same element.\n\t\t\t}\n\t\t\tvars.onKill && vars.onKill(self);\n\t\t};\n\n\t\t_triggers.push(self);\n\t\tself.enable(false, false);\n\t\tcustomRevertReturn && customRevertReturn(self);\n\n\t\tif (animation && animation.add && !change) { // if the animation is a timeline, it may not have been populated yet, so it wouldn't render at the proper place on the first refresh(), thus we should schedule one for the next tick. If \"change\" is defined, we know it must be re-enabling, thus we can refresh() right away.\n\t\t\tlet updateFunc = self.update; // some browsers may fire a scroll event BEFORE a tick elapses and/or the DOMContentLoaded fires. So there's a chance update() will be called BEFORE a refresh() has happened on a Timeline-attached ScrollTrigger which means the start/end won't be calculated yet. We don't want to add conditional logic inside the update() method (like check to see if end is defined and if not, force a refresh()) because that's a function that gets hit a LOT (performance). So we swap out the real update() method for this one that'll re-attach it the first time it gets called and of course forces a refresh().\n\t\t\tself.update = () => {\n\t\t\t\tself.update = updateFunc;\n\t\t\t\tstart || end || self.refresh();\n\t\t\t};\n\t\t\tgsap.delayedCall(0.01, self.update);\n\t\t\tchange = 0.01;\n\t\t\tstart = end = 0;\n\t\t} else {\n\t\t\tself.refresh();\n\t\t}\n\t\tpin && _queueRefreshAll(); // pinning could affect the positions of other things, so make sure we queue a full refresh()\n\t}\n\n\n\tstatic register(core) {\n\t\tif (!_coreInitted) {\n\t\t\tgsap = core || _getGSAP();\n\t\t\t_windowExists() && window.document && ScrollTrigger.enable();\n\t\t\t_coreInitted = _enabled;\n\t\t}\n\t\treturn _coreInitted;\n\t}\n\n\tstatic defaults(config) {\n\t\tif (config) {\n\t\t\tfor (let p in config) {\n\t\t\t\t_defaults[p] = config[p];\n\t\t\t}\n\t\t}\n\t\treturn _defaults;\n\t}\n\n\tstatic disable(reset, kill) {\n\t\t_enabled = 0;\n\t\t_triggers.forEach(trigger => trigger[kill ? \"kill\" : \"disable\"](reset));\n\t\t_removeListener(_win, \"wheel\", _onScroll);\n\t\t_removeListener(_doc, \"scroll\", _onScroll);\n\t\tclearInterval(_syncInterval);\n\t\t_removeListener(_doc, \"touchcancel\", _passThrough);\n\t\t_removeListener(_body, \"touchstart\", _passThrough);\n\t\t_multiListener(_removeListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\t\t_multiListener(_removeListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\t\t_resizeDelay.kill();\n\t\t_iterateAutoRefresh(_removeListener);\n\t\tfor (let i = 0; i < _scrollers.length; i+=3) {\n\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+1]);\n\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+2]);\n\t\t}\n\t}\n\n\tstatic enable() {\n\t\t_win = window;\n\t\t_doc = document;\n\t\t_docEl = _doc.documentElement;\n\t\t_body = _doc.body;\n\t\tif (gsap) {\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t_context = gsap.core.context || _passThrough;\n\t\t\t_suppressOverwrites = gsap.core.suppressOverwrites || _passThrough;\n\t\t\t_scrollRestoration = _win.history.scrollRestoration || \"auto\";\n\t\t\t_lastScroll = _win.pageYOffset;\n\t\t\tgsap.core.globals(\"ScrollTrigger\", ScrollTrigger); // must register the global manually because in Internet Explorer, functions (classes) don't have a \"name\" property.\n\t\t\tif (_body) {\n\t\t\t\t_enabled = 1;\n\t\t\t\t_div100vh = document.createElement(\"div\"); // to solve mobile browser address bar show/hide resizing, we shouldn't rely on window.innerHeight. Instead, use a <div> with its height set to 100vh and measure that since that's what the scrolling is based on anyway and it's not affected by address bar showing/hiding.\n\t\t\t\t_div100vh.style.height = \"100vh\";\n\t\t\t\t_div100vh.style.position = \"absolute\";\n\t\t\t\t_refresh100vh();\n\t\t\t\t_rafBugFix();\n\t\t\t\tObserver.register(gsap);\n\t\t\t\t// isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\t\t\t\tScrollTrigger.isTouch = Observer.isTouch;\n\t\t\t\t_fixIOSBug = Observer.isTouch && /(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent); // since 2017, iOS has had a bug that causes event.clientX/Y to be inaccurate when a scroll occurs, thus we must alternate ignoring every other touchmove event to work around it. See https://bugs.webkit.org/show_bug.cgi?id=181954 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503\n\t\t\t\t_addListener(_win, \"wheel\", _onScroll); // mostly for 3rd party smooth scrolling libraries.\n\t\t\t\t_root = [_win, _doc, _docEl, _body];\n\t\t\t\tif (gsap.matchMedia) {\n\t\t\t\t\tScrollTrigger.matchMedia = vars => {\n\t\t\t\t\t\tlet mm = gsap.matchMedia(),\n\t\t\t\t\t\t\tp;\n\t\t\t\t\t\tfor (p in vars) {\n\t\t\t\t\t\t\tmm.add(p, vars[p]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn mm;\n\t\t\t\t\t};\n\t\t\t\t\tgsap.addEventListener(\"matchMediaInit\", () => _revertAll());\n\t\t\t\t\tgsap.addEventListener(\"matchMediaRevert\", () => _revertRecorded());\n\t\t\t\t\tgsap.addEventListener(\"matchMedia\", () => {\n\t\t\t\t\t\t_refreshAll(0, 1);\n\t\t\t\t\t\t_dispatch(\"matchMedia\");\n\t\t\t\t\t});\n\t\t\t\t\tgsap.matchMedia(\"(orientation: portrait)\", () => { // when orientation changes, we should take new base measurements for the ignoreMobileResize feature.\n\t\t\t\t\t\t_setBaseDimensions();\n\t\t\t\t\t\treturn _setBaseDimensions;\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tconsole.warn(\"Requires GSAP 3.11.0 or later\");\n\t\t\t\t}\n\t\t\t\t_setBaseDimensions();\n\t\t\t\t_addListener(_doc, \"scroll\", _onScroll); // some browsers (like Chrome), the window stops dispatching scroll events on the window if you scroll really fast, but it's consistent on the document!\n\t\t\t\tlet bodyStyle = _body.style,\n\t\t\t\t\tborder = bodyStyle.borderTopStyle,\n\t\t\t\t\tAnimationProto = gsap.core.Animation.prototype,\n\t\t\t\t\tbounds, i;\n\t\t\t\tAnimationProto.revert || Object.defineProperty(AnimationProto, \"revert\", { value: function() { return this.time(-0.01, true); }}); // only for backwards compatibility (Animation.revert() was added after 3.10.4)\n\t\t\t\tbodyStyle.borderTopStyle = \"solid\"; // works around an issue where a margin of a child element could throw off the bounds of the _body, making it seem like there's a margin when there actually isn't. The border ensures that the bounds are accurate.\n\t\t\t\tbounds = _getBounds(_body);\n\t\t\t\t_vertical.m = Math.round(bounds.top + _vertical.sc()) || 0; // accommodate the offset of the <body> caused by margins and/or padding\n\t\t\t\t_horizontal.m = Math.round(bounds.left + _horizontal.sc()) || 0;\n\t\t\t\tborder ? (bodyStyle.borderTopStyle = border) : bodyStyle.removeProperty(\"border-top-style\");\n\t\t\t\t// TODO: (?) maybe move to leveraging the velocity mechanism in Observer and skip intervals.\n\t\t\t\t_syncInterval = setInterval(_sync, 250);\n\t\t\t\tgsap.delayedCall(0.5, () => _startup = 0);\n\t\t\t\t_addListener(_doc, \"touchcancel\", _passThrough); // some older Android devices intermittently stop dispatching \"touchmove\" events if we don't listen for \"touchcancel\" on the document.\n\t\t\t\t_addListener(_body, \"touchstart\", _passThrough); //works around Safari bug: https://greensock.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\t\t\t\t_multiListener(_addListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\t\t\t\t_multiListener(_addListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\t\t\t\t_transformProp = gsap.utils.checkPrefix(\"transform\");\n\t\t\t\t_stateProps.push(_transformProp);\n\t\t\t\t_coreInitted = _getTime();\n\t\t\t\t_resizeDelay = gsap.delayedCall(0.2, _refreshAll).pause();\n\t\t\t\t_autoRefresh = [_doc, \"visibilitychange\", () => {\n\t\t\t\t\tlet w = _win.innerWidth,\n\t\t\t\t\t\th = _win.innerHeight;\n\t\t\t\t\tif (_doc.hidden) {\n\t\t\t\t\t\t_prevWidth = w;\n\t\t\t\t\t\t_prevHeight = h;\n\t\t\t\t\t} else if (_prevWidth !== w || _prevHeight !== h) {\n\t\t\t\t\t\t_onResize();\n\t\t\t\t\t}\n\t\t\t\t}, _doc, \"DOMContentLoaded\", _refreshAll, _win, \"load\", _refreshAll, _win, \"resize\", _onResize];\n\t\t\t\t_iterateAutoRefresh(_addListener);\n\t\t\t\t_triggers.forEach(trigger => trigger.enable(0, 1));\n\t\t\t\tfor (i = 0; i < _scrollers.length; i+=3) {\n\t\t\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+1]);\n\t\t\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+2]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tstatic config(vars) {\n\t\t(\"limitCallbacks\" in vars) && (_limitCallbacks = !!vars.limitCallbacks);\n\t\tlet ms = vars.syncInterval;\n\t\tms && clearInterval(_syncInterval) || ((_syncInterval = ms) && setInterval(_sync, ms));\n\t\t(\"ignoreMobileResize\" in vars) && (_ignoreMobileResize = ScrollTrigger.isTouch === 1 && vars.ignoreMobileResize);\n\t\tif (\"autoRefreshEvents\" in vars) {\n\t\t\t_iterateAutoRefresh(_removeListener) || _iterateAutoRefresh(_addListener, vars.autoRefreshEvents || \"none\");\n\t\t\t_ignoreResize = (vars.autoRefreshEvents + \"\").indexOf(\"resize\") === -1;\n\t\t}\n\t}\n\n\tstatic scrollerProxy(target, vars) {\n\t\tlet t = _getTarget(target),\n\t\t\ti = _scrollers.indexOf(t),\n\t\t\tisViewport = _isViewport(t);\n\t\tif (~i) {\n\t\t\t_scrollers.splice(i, isViewport ? 6 : 2);\n\t\t}\n\t\tif (vars) {\n\t\t\tisViewport ? _proxies.unshift(_win, vars, _body, vars, _docEl, vars) : _proxies.unshift(t, vars);\n\t\t}\n\t}\n\n\tstatic clearMatchMedia(query) {\n\t\t_triggers.forEach(t => t._ctx && t._ctx.query === query && t._ctx.kill(true, true));\n\t}\n\n\tstatic isInViewport(element, ratio, horizontal) {\n\t\tlet bounds = (_isString(element) ? _getTarget(element) : element).getBoundingClientRect(),\n\t\t\toffset = bounds[horizontal ? _width : _height] * ratio || 0;\n\t\treturn horizontal ? bounds.right - offset > 0 && bounds.left + offset < _win.innerWidth : bounds.bottom - offset > 0 && bounds.top + offset < _win.innerHeight;\n\t}\n\n\tstatic positionInViewport(element, referencePoint, horizontal) {\n\t\t_isString(element) && (element = _getTarget(element));\n\t\tlet bounds = element.getBoundingClientRect(),\n\t\t\tsize = bounds[horizontal ? _width : _height],\n\t\t\toffset = referencePoint == null ? size / 2 : ((referencePoint in _keywords) ? _keywords[referencePoint] * size : ~referencePoint.indexOf(\"%\") ? parseFloat(referencePoint) * size / 100 : parseFloat(referencePoint) || 0);\n\t\treturn horizontal ? (bounds.left + offset) / _win.innerWidth : (bounds.top + offset) / _win.innerHeight;\n\t}\n\n\tstatic killAll(allowListeners) {\n\t\t_triggers.slice(0).forEach(t => t.vars.id !== \"ScrollSmoother\" && t.kill());\n\t\tif (allowListeners !== true) {\n\t\t\tlet listeners = _listeners.killAll || [];\n\t\t\t_listeners = {};\n\t\t\tlisteners.forEach(f => f());\n\t\t}\n\t}\n\n}\n\nScrollTrigger.version = \"3.12.2\";\nScrollTrigger.saveStyles = targets => targets ? _toArray(targets).forEach(target => { // saved styles are recorded in a consecutive alternating Array, like [element, cssText, transform attribute, cache, matchMedia, ...]\n\tif (target && target.style) {\n\t\tlet i = _savedStyles.indexOf(target);\n\t\ti >= 0 && _savedStyles.splice(i, 5);\n\t\t_savedStyles.push(target, target.style.cssText, target.getBBox && target.getAttribute(\"transform\"), gsap.core.getCache(target), _context());\n\t}\n}) : _savedStyles;\nScrollTrigger.revert = (soft, media) => _revertAll(!soft, media);\nScrollTrigger.create = (vars, animation) => new ScrollTrigger(vars, animation);\nScrollTrigger.refresh = safe => safe ? _onResize() : (_coreInitted || ScrollTrigger.register()) && _refreshAll(true);\nScrollTrigger.update = force => ++_scrollers.cache && _updateAll(force === true ? 2 : 0);\nScrollTrigger.clearScrollMemory = _clearScrollMemory;\nScrollTrigger.maxScroll = (element, horizontal) => _maxScroll(element, horizontal ? _horizontal : _vertical);\nScrollTrigger.getScrollFunc = (element, horizontal) => _getScrollFunc(_getTarget(element), horizontal ? _horizontal : _vertical);\nScrollTrigger.getById = id => _ids[id];\nScrollTrigger.getAll = () => _triggers.filter(t => t.vars.id !== \"ScrollSmoother\"); // it's common for people to ScrollTrigger.getAll(t => t.kill()) on page routes, for example, and we don't want it to ruin smooth scrolling by killing the main ScrollSmoother one.\nScrollTrigger.isScrolling = () => !!_lastScrollTime;\nScrollTrigger.snapDirectional = _snapDirectional;\nScrollTrigger.addEventListener = (type, callback) => {\n\tlet a = _listeners[type] || (_listeners[type] = []);\n\t~a.indexOf(callback) || a.push(callback);\n};\nScrollTrigger.removeEventListener = (type, callback) => {\n\tlet a = _listeners[type],\n\t\ti = a && a.indexOf(callback);\n\ti >= 0 && a.splice(i, 1);\n};\nScrollTrigger.batch = (targets, vars) => {\n\tlet result = [],\n\t\tvarsCopy = {},\n\t\tinterval = vars.interval || 0.016,\n\t\tbatchMax = vars.batchMax || 1e9,\n\t\tproxyCallback = (type, callback) => {\n\t\t\tlet elements = [],\n\t\t\t\ttriggers = [],\n\t\t\t\tdelay = gsap.delayedCall(interval, () => {callback(elements, triggers); elements = []; triggers = [];}).pause();\n\t\t\treturn self => {\n\t\t\t\telements.length || delay.restart(true);\n\t\t\t\telements.push(self.trigger);\n\t\t\t\ttriggers.push(self);\n\t\t\t\tbatchMax <= elements.length && delay.progress(1);\n\t\t\t};\n\t\t},\n\t\tp;\n\tfor (p in vars) {\n\t\tvarsCopy[p] = (p.substr(0, 2) === \"on\" && _isFunction(vars[p]) && p !== \"onRefreshInit\") ? proxyCallback(p, vars[p]) : vars[p];\n\t}\n\tif (_isFunction(batchMax)) {\n\t\tbatchMax = batchMax();\n\t\t_addListener(ScrollTrigger, \"refresh\", () => batchMax = vars.batchMax());\n\t}\n\t_toArray(targets).forEach(target => {\n\t\tlet config = {};\n\t\tfor (p in varsCopy) {\n\t\t\tconfig[p] = varsCopy[p];\n\t\t}\n\t\tconfig.trigger = target;\n\t\tresult.push(ScrollTrigger.create(config));\n\t});\n\treturn result;\n}\n\n\n// to reduce file size. clamps the scroll and also returns a duration multiplier so that if the scroll gets chopped shorter, the duration gets curtailed as well (otherwise if you're very close to the top of the page, for example, and swipe up really fast, it'll suddenly slow down and take a long time to reach the top).\nlet _clampScrollAndGetDurationMultiplier = (scrollFunc, current, end, max) => {\n\t\tcurrent > max ? scrollFunc(max) : current < 0 && scrollFunc(0);\n\t\treturn end > max ? (max - current) / (end - current) : end < 0 ? current / (current - end) : 1;\n\t},\n\t_allowNativePanning = (target, direction) => {\n\t\tif (direction === true) {\n\t\t\ttarget.style.removeProperty(\"touch-action\");\n\t\t} else {\n\t\t\ttarget.style.touchAction = direction === true ? \"auto\" : direction ? \"pan-\" + direction + (Observer.isTouch ? \" pinch-zoom\" : \"\") : \"none\"; // note: Firefox doesn't support it pinch-zoom properly, at least in addition to a pan-x or pan-y.\n\t\t}\n\t\ttarget === _docEl && _allowNativePanning(_body, direction);\n\t},\n\t_overflow = {auto: 1, scroll: 1},\n\t_nestedScroll = ({event, target, axis}) => {\n\t\tlet node = (event.changedTouches ? event.changedTouches[0] : event).target,\n\t\t\tcache = node._gsap || gsap.core.getCache(node),\n\t\t\ttime = _getTime(), cs;\n\t\tif (!cache._isScrollT || time - cache._isScrollT > 2000) { // cache for 2 seconds to improve performance.\n\t\t\twhile (node && node !== _body && ((node.scrollHeight <= node.clientHeight && node.scrollWidth <= node.clientWidth) || !(_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]))) node = node.parentNode;\n\t\t\tcache._isScroll = node && node !== target && !_isViewport(node) && (_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]);\n\t\t\tcache._isScrollT = time;\n\t\t}\n\t\tif (cache._isScroll || axis === \"x\") {\n\t\t\tevent.stopPropagation();\n\t\t\tevent._gsapAllow = true;\n\t\t}\n\t},\n\t// capture events on scrollable elements INSIDE the <body> and allow those by calling stopPropagation() when we find a scrollable ancestor\n\t_inputObserver = (target, type, inputs, nested) => Observer.create({\n\t\ttarget: target,\n\t\tcapture: true,\n\t\tdebounce: false,\n\t\tlockAxis: true,\n\t\ttype: type,\n\t\tonWheel: (nested = nested && _nestedScroll),\n\t\tonPress: nested,\n\t\tonDrag: nested,\n\t\tonScroll: nested,\n\t\tonEnable: () => inputs && _addListener(_doc, Observer.eventTypes[0], _captureInputs, false, true),\n\t\tonDisable: () => _removeListener(_doc, Observer.eventTypes[0], _captureInputs, true)\n\t}),\n\t_inputExp = /(input|label|select|textarea)/i,\n\t_inputIsFocused,\n\t_captureInputs = e => {\n\t\tlet isInput = _inputExp.test(e.target.tagName);\n\t\tif (isInput || _inputIsFocused) {\n\t\t\te._gsapAllow = true;\n\t\t\t_inputIsFocused = isInput;\n\t\t}\n\t},\n\t_getScrollNormalizer = vars => {\n\t\t_isObject(vars) || (vars = {});\n\t\tvars.preventDefault = vars.isNormalizer = vars.allowClicks = true;\n\t\tvars.type || (vars.type = \"wheel,touch\");\n\t\tvars.debounce = !!vars.debounce;\n\t\tvars.id = vars.id || \"normalizer\";\n\t\tlet {normalizeScrollX, momentum, allowNestedScroll, onRelease} = vars,\n\t\t\tself, maxY,\n\t\t\ttarget = _getTarget(vars.target) || _docEl,\n\t\t\tsmoother = gsap.core.globals().ScrollSmoother,\n\t\t\tsmootherInstance = smoother && smoother.get(),\n\t\t\tcontent = _fixIOSBug && ((vars.content && _getTarget(vars.content)) || (smootherInstance && vars.content !== false && !smootherInstance.smooth() && smootherInstance.content())),\n\t\t\tscrollFuncY = _getScrollFunc(target, _vertical),\n\t\t\tscrollFuncX = _getScrollFunc(target, _horizontal),\n\t\t\tscale = 1,\n\t\t\tinitialScale = (Observer.isTouch && _win.visualViewport ? _win.visualViewport.scale * _win.visualViewport.width : _win.outerWidth) / _win.innerWidth,\n\t\t\twheelRefresh = 0,\n\t\t\tresolveMomentumDuration = _isFunction(momentum) ? () => momentum(self) : () => momentum || 2.8,\n\t\t\tlastRefreshID, skipTouchMove,\n\t\t\tinputObserver = _inputObserver(target, vars.type, true, allowNestedScroll),\n\t\t\tresumeTouchMove = () => skipTouchMove = false,\n\t\t\tscrollClampX = _passThrough,\n\t\t\tscrollClampY = _passThrough,\n\t\t\tupdateClamps = () => {\n\t\t\t\tmaxY = _maxScroll(target, _vertical);\n\t\t\t\tscrollClampY = _clamp(_fixIOSBug ? 1 : 0, maxY);\n\t\t\t\tnormalizeScrollX && (scrollClampX = _clamp(0, _maxScroll(target, _horizontal)));\n\t\t\t\tlastRefreshID = _refreshID;\n\t\t\t},\n\t\t\tremoveContentOffset = () => {\n\t\t\t\tcontent._gsap.y = _round(parseFloat(content._gsap.y) + scrollFuncY.offset) + \"px\";\n\t\t\t\tcontent.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + parseFloat(content._gsap.y) + \", 0, 1)\";\n\t\t\t\tscrollFuncY.offset = scrollFuncY.cacheID = 0;\n\t\t\t},\n\t\t\tignoreDrag = () => {\n\t\t\t\tif (skipTouchMove) {\n\t\t\t\t\trequestAnimationFrame(resumeTouchMove);\n\t\t\t\t\tlet offset = _round(self.deltaY / 2),\n\t\t\t\t\t\tscroll = scrollClampY(scrollFuncY.v - offset);\n\t\t\t\t\tif (content && scroll !== scrollFuncY.v + scrollFuncY.offset) {\n\t\t\t\t\t\tscrollFuncY.offset = scroll - scrollFuncY.v;\n\t\t\t\t\t\tlet y = _round((parseFloat(content && content._gsap.y) || 0) - scrollFuncY.offset);\n\t\t\t\t\t\tcontent.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + y + \", 0, 1)\";\n\t\t\t\t\t\tcontent._gsap.y = y + \"px\";\n\t\t\t\t\t\tscrollFuncY.cacheID = _scrollers.cache;\n\t\t\t\t\t\t_updateAll();\n\t\t\t\t\t}\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\t\tskipTouchMove = true;\n\t\t\t},\n\t\t\ttween, startScrollX, startScrollY, onStopDelayedCall,\n\t\t\tonResize = () => { // if the window resizes, like on an iPhone which Apple FORCES the address bar to show/hide even if we event.preventDefault(), it may be scrolling too far now that the address bar is showing, so we must dynamically adjust the momentum tween.\n\t\t\t\tupdateClamps();\n\t\t\t\tif (tween.isActive() && tween.vars.scrollY > maxY) {\n\t\t\t\t\tscrollFuncY() > maxY ? tween.progress(1) && scrollFuncY(maxY) : tween.resetTo(\"scrollY\", maxY);\n\t\t\t\t}\n\t\t\t};\n\t\tcontent && gsap.set(content, {y: \"+=0\"}); // to ensure there's a cache (element._gsap)\n\t\tvars.ignoreCheck = e => (_fixIOSBug && e.type === \"touchmove\" && ignoreDrag(e)) || (scale > 1.05 && e.type !== \"touchstart\") || self.isGesturing || (e.touches && e.touches.length > 1);\n\t\tvars.onPress = () => {\n\t\t\tskipTouchMove = false;\n\t\t\tlet prevScale = scale;\n\t\t\tscale = _round(((_win.visualViewport && _win.visualViewport.scale) || 1) / initialScale);\n\t\t\ttween.pause();\n\t\t\tprevScale !== scale && _allowNativePanning(target, scale > 1.01 ? true : normalizeScrollX ? false : \"x\");\n\t\t\tstartScrollX = scrollFuncX();\n\t\t\tstartScrollY = scrollFuncY();\n\t\t\tupdateClamps();\n\t\t\tlastRefreshID = _refreshID;\n\t\t}\n\t\tvars.onRelease = vars.onGestureStart = (self, wasDragging) => {\n\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\tif (!wasDragging) {\n\t\t\t\tonStopDelayedCall.restart(true);\n\t\t\t} else {\n\t\t\t\t_scrollers.cache++; // make sure we're pulling the non-cached value\n\t\t\t\t// alternate algorithm: durX = Math.min(6, Math.abs(self.velocityX / 800)),\tdur = Math.max(durX, Math.min(6, Math.abs(self.velocityY / 800))); dur = dur * (0.4 + (1 - _power4In(dur / 6)) * 0.6)) * (momentumSpeed || 1)\n\t\t\t\tlet dur = resolveMomentumDuration(),\n\t\t\t\t\tcurrentScroll, endScroll;\n\t\t\t\tif (normalizeScrollX) {\n\t\t\t\t\tcurrentScroll = scrollFuncX();\n\t\t\t\t\tendScroll = currentScroll + (dur * 0.05 * -self.velocityX) / 0.227; // the constant .227 is from power4(0.05). velocity is inverted because scrolling goes in the opposite direction.\n\t\t\t\t\tdur *= _clampScrollAndGetDurationMultiplier(scrollFuncX, currentScroll, endScroll, _maxScroll(target, _horizontal));\n\t\t\t\t\ttween.vars.scrollX = scrollClampX(endScroll);\n\t\t\t\t}\n\t\t\t\tcurrentScroll = scrollFuncY();\n\t\t\t\tendScroll = currentScroll + (dur * 0.05 * -self.velocityY) / 0.227; // the constant .227 is from power4(0.05)\n\t\t\t\tdur *= _clampScrollAndGetDurationMultiplier(scrollFuncY, currentScroll, endScroll, _maxScroll(target, _vertical));\n\t\t\t\ttween.vars.scrollY = scrollClampY(endScroll);\n\t\t\t\ttween.invalidate().duration(dur).play(0.01);\n\t\t\t\tif (_fixIOSBug && tween.vars.scrollY >= maxY || currentScroll >= maxY-1) { // iOS bug: it'll show the address bar but NOT fire the window \"resize\" event until the animation is done but we must protect against overshoot so we leverage an onUpdate to do so.\n\t\t\t\t\tgsap.to({}, {onUpdate: onResize, duration: dur});\n\t\t\t\t}\n\t\t\t}\n\t\t\tonRelease && onRelease(self);\n\t\t};\n\t\tvars.onWheel = () => {\n\t\t\ttween._ts && tween.pause();\n\t\t\tif (_getTime() - wheelRefresh > 1000) { // after 1 second, refresh the clamps otherwise that'll only happen when ScrollTrigger.refresh() is called or for touch-scrolling.\n\t\t\t\tlastRefreshID = 0;\n\t\t\t\twheelRefresh = _getTime();\n\t\t\t}\n\t\t};\n\t\tvars.onChange = (self, dx, dy, xArray, yArray) => {\n\t\t\t_refreshID !== lastRefreshID && updateClamps();\n\t\t\tdx && normalizeScrollX && scrollFuncX(scrollClampX(xArray[2] === dx ? startScrollX + (self.startX - self.x) : scrollFuncX() + dx - xArray[1])); // for more precision, we track pointer/touch movement from the start, otherwise it'll drift.\n\t\t\tif (dy) {\n\t\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\t\tlet isTouch = yArray[2] === dy,\n\t\t\t\t\ty = isTouch ? startScrollY + self.startY - self.y : scrollFuncY() + dy - yArray[1],\n\t\t\t\t\tyClamped = scrollClampY(y);\n\t\t\t\tisTouch && y !== yClamped && (startScrollY += yClamped - y);\n\t\t\t\tscrollFuncY(yClamped);\n\t\t\t}\n\t\t\t(dy || dx) && _updateAll();\n\t\t};\n\t\tvars.onEnable = () => {\n\t\t\t_allowNativePanning(target, normalizeScrollX ? false : \"x\");\n\t\t\tScrollTrigger.addEventListener(\"refresh\", onResize);\n\t\t\t_addListener(_win, \"resize\", onResize);\n\t\t\tif (scrollFuncY.smooth) {\n\t\t\t\tscrollFuncY.target.style.scrollBehavior = \"auto\";\n\t\t\t\tscrollFuncY.smooth = scrollFuncX.smooth = false;\n\t\t\t}\n\t\t\tinputObserver.enable();\n\t\t};\n\t\tvars.onDisable = () => {\n\t\t\t_allowNativePanning(target, true);\n\t\t\t_removeListener(_win, \"resize\", onResize);\n\t\t\tScrollTrigger.removeEventListener(\"refresh\", onResize);\n\t\t\tinputObserver.kill();\n\t\t};\n\t\tvars.lockAxis = vars.lockAxis !== false;\n\t\tself = new Observer(vars);\n\t\tself.iOS = _fixIOSBug; // used in the Observer getCachedScroll() function to work around an iOS bug that wreaks havoc with TouchEvent.clientY if we allow scroll to go all the way back to 0.\n\t\t_fixIOSBug && !scrollFuncY() && scrollFuncY(1); // iOS bug causes event.clientY values to freak out (wildly inaccurate) if the scroll position is exactly 0.\n\t\t_fixIOSBug && gsap.ticker.add(_passThrough); // prevent the ticker from sleeping\n\t\tonStopDelayedCall = self._dc;\n\t\ttween = gsap.to(self, {ease: \"power4\", paused: true, scrollX: normalizeScrollX ? \"+=0.1\" : \"+=0\", scrollY: \"+=0.1\", modifiers: {scrollY: _interruptionTracker(scrollFuncY, scrollFuncY(), () => tween.pause())\t}, onUpdate: _updateAll, onComplete: onStopDelayedCall.vars.onComplete}); // we need the modifier to sense if the scroll position is altered outside of the momentum tween (like with a scrollTo tween) so we can pause() it to prevent conflicts.\n\t\treturn self;\n\t};\n\nScrollTrigger.sort = func => _triggers.sort(func || ((a, b) => (a.vars.refreshPriority || 0) * -1e6 + a.start - (b.start + (b.vars.refreshPriority || 0) * -1e6)));\nScrollTrigger.observe = vars => new Observer(vars);\nScrollTrigger.normalizeScroll = vars => {\n\tif (typeof(vars) === \"undefined\") {\n\t\treturn _normalizer;\n\t}\n\tif (vars === true && _normalizer) {\n\t\treturn _normalizer.enable();\n\t}\n\tif (vars === false) {\n\t\treturn _normalizer && _normalizer.kill();\n\t}\n\tlet normalizer = vars instanceof Observer ? vars : _getScrollNormalizer(vars);\n\t_normalizer && _normalizer.target === normalizer.target && _normalizer.kill();\n\t_isViewport(normalizer.target) && (_normalizer = normalizer);\n\treturn normalizer;\n};\n\n\nScrollTrigger.core = { // smaller file size way to leverage in ScrollSmoother and Observer\n\t_getVelocityProp,\n\t_inputObserver,\n\t_scrollers,\n\t_proxies,\n\tbridge: {\n\t\t// when normalizeScroll sets the scroll position (ss = setScroll)\n\t\tss: () => {\n\t\t\t_lastScrollTime || _dispatch(\"scrollStart\");\n\t\t\t_lastScrollTime = _getTime();\n\t\t},\n\t\t// a way to get the _refreshing value in Observer\n\t\tref: () => _refreshing\n\t}\n};\n\n_getGSAP() && gsap.registerPlugin(ScrollTrigger);\n\nexport { ScrollTrigger as default };"], "names": ["_getGSAP", "gsap", "window", "registerPlugin", "_getProxyProp", "element", "property", "_proxies", "indexOf", "_isViewport", "el", "_root", "_addListener", "type", "func", "nonPassive", "capture", "addEventListener", "passive", "_removeListener", "removeEventListener", "_onScroll", "_normalizer", "isPressed", "_scrollers", "cache", "_scrollCacheFunc", "f", "doNotCache", "cachingFunc", "value", "_startup", "_win", "history", "scrollRestoration", "isNormalizing", "v", "Math", "round", "iOS", "cacheID", "_bridge", "offset", "_getTarget", "t", "self", "_ctx", "selector", "utils", "toArray", "config", "null<PERSON><PERSON><PERSON><PERSON><PERSON>n", "console", "warn", "_getScrollFunc", "s", "sc", "_doc", "scrollingElement", "_docEl", "i", "_vertical", "push", "prev", "arguments", "length", "target", "smooth", "getProperty", "_getVelocityProp", "minTimeRefresh", "useDel<PERSON>", "update", "force", "_getTime", "min", "t1", "v2", "v1", "t2", "dropToZeroTime", "max", "reset", "getVelocity", "latestValue", "tOld", "vOld", "_getEvent", "e", "preventDefault", "_gsapAllow", "changedTouches", "_getAbsoluteMax", "a", "abs", "_setScrollTrigger", "ScrollTrigger", "core", "globals", "_integrate", "data", "bridge", "scrollers", "proxies", "name", "_initCore", "document", "body", "documentElement", "_body", "clamp", "_context", "context", "_pointerType", "_isTouch", "Observer", "is<PERSON><PERSON>ch", "matchMedia", "matches", "navigator", "maxTouchPoints", "msMaxTouchPoints", "_eventTypes", "eventTypes", "split", "setTimeout", "_coreInitted", "_observers", "Date", "now", "_scrollLeft", "_scrollTop", "_horizontal", "p", "p2", "os", "os2", "d", "d2", "scrollTo", "pageXOffset", "op", "pageYOffset", "init", "vars", "tolerance", "dragMinimum", "lineHeight", "debounce", "onStop", "onStopDelay", "ignore", "wheelSpeed", "event", "onDragStart", "onDragEnd", "onDrag", "onPress", "onRelease", "onRight", "onLeft", "onUp", "onDown", "onChangeX", "onChangeY", "onChange", "onToggleX", "onToggleY", "onHover", "onHoverEnd", "onMove", "<PERSON><PERSON><PERSON><PERSON>", "isNormalizer", "onGestureStart", "onGestureEnd", "onWheel", "onEnable", "onDisable", "onClick", "scrollSpeed", "allowClicks", "lockAxis", "onLockAxis", "clickCapture", "onClickTime", "_ignore<PERSON>heck", "isPointerOr<PERSON>ouch", "limitToTouch", "pointerType", "dx", "deltaX", "dy", "deltaY", "changedX", "changedY", "prevDeltaX", "prevDeltaY", "moved", "dragged", "locked", "wheeled", "id", "onDelta", "x", "y", "index", "_vx", "_vy", "requestAnimationFrame", "onTouchOrPointerDelta", "axis", "_onDrag", "clientX", "clientY", "isDragging", "startX", "startY", "_onGestureStart", "touches", "isGesturing", "_onGestureEnd", "onScroll", "scrollFuncX", "scrollFuncY", "scrollX", "scrollY", "onStopDelayedCall", "restart", "_onWheel", "multiplier", "deltaMode", "innerHeight", "_onMove", "_onHover", "_onHoverEnd", "_onClick", "parseFloat", "getComputedStyle", "this", "isViewport", "ownerDoc", "ownerDocument", "_onPress", "button", "pause", "_onRelease", "isTrackingDrag", "isNaN", "wasDragging", "eventData", "delayedCall", "defaultPrevented", "click", "createEvent", "syntheticEvent", "initMouseEvent", "screenX", "screenY", "dispatchEvent", "_dc", "onStopFunc", "enable", "isEnabled", "disable", "filter", "o", "kill", "revert", "splice", "version", "create", "register", "getAll", "slice", "getById", "_parseClamp", "_isString", "substr", "_keepClamp", "_pointerDownHandler", "_pointerIsDown", "_pointerU<PERSON><PERSON><PERSON><PERSON>", "_passThrough", "_round", "_windowExists", "_getViewportDimension", "dimensionProperty", "_100vh", "_getBoundsFunc", "_winOffsets", "width", "innerWidth", "height", "_getBounds", "_maxScroll", "_iterateAutoRefresh", "events", "_autoRefresh", "_isFunction", "_isNumber", "_isObject", "_endAnimation", "animation", "reversed", "progress", "_callback", "enabled", "result", "totalTime", "callbackAnimation", "_getComputedStyle", "_setDefaults", "obj", "defaults", "_getSize", "_getLabelRatioArray", "timeline", "labels", "duration", "_snapDirectional", "snapIncrementOrArray", "snap", "Array", "isArray", "sort", "b", "direction", "threshold", "snapped", "_multiListener", "types", "callback", "for<PERSON>ach", "_wheelListener", "scrollFunc", "wheelHandler", "_offsetToPx", "size", "eqIndex", "relative", "char<PERSON>t", "_keywords", "_createMarker", "container", "matchWidthEl", "containerAnimation", "startColor", "endColor", "fontSize", "indent", "fontWeight", "createElement", "useFixedPosition", "isScroller", "parent", "isStart", "color", "css", "_right", "_bottom", "offsetWidth", "_isStart", "setAttribute", "style", "cssText", "innerText", "children", "insertBefore", "append<PERSON><PERSON><PERSON>", "_offset", "_position<PERSON><PERSON>er", "_sync", "_lastScrollTime", "_rafID", "_updateAll", "clientWidth", "_dispatch", "_setBaseDimensions", "_baseScreenWidth", "_baseScreenHeight", "_onResize", "_refreshing", "_ignoreResize", "fullscreenElement", "webkitFullscreenElement", "_ignoreMobileResize", "_resizeDelay", "_softRefresh", "_refreshAll", "_revertRecorded", "media", "_savedStyles", "query", "getBBox", "uncache", "_revertAll", "trigger", "_i", "_triggers", "_clearScrollMemory", "_refreshingAll", "rec", "_scrollRestoration", "_refresh100vh", "_div100vh", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON>", "_swapPinIn", "pin", "spacer", "cs", "spacerState", "_gsap", "swappedIn", "_propNamesToCopy", "spacerStyle", "pinStyle", "position", "display", "flexBasis", "overflow", "boxSizing", "_width", "_px", "_height", "_padding", "_margin", "_setState", "parentNode", "_getState", "l", "_stateProps", "state", "_parsePosition", "scrollerSize", "scroll", "marker", "markerScroller", "scrollerBounds", "borderWidth", "scrollerMax", "clampZeroProp", "p1", "time", "seek", "mapRange", "scrollTrigger", "start", "end", "bounds", "localOffset", "globalOffset", "offsets", "left", "top", "removeProperty", "m", "_caScrollDist", "_reparent", "_st<PERSON><PERSON>", "_prefixExp", "test", "getCache", "_interruptionTracker", "getValueFunc", "initialValue", "onInterrupt", "last1", "last2", "current", "_shiftMarker", "set", "_getTweenCreator", "scroller", "getTween", "change1", "change2", "tween", "onComplete", "modifiers", "getScroll", "checkForInterruption", "prop", "ratio", "onUpdate", "call", "to", "_toArray", "_clamp", "_time2", "_syncInterval", "_transformProp", "_prevWidth", "_prevHeight", "_sort", "_suppressOverwrites", "_fixIOSBug", "_limitCallbacks", "_queueRefreshID", "_primary", "_time1", "_enabled", "_abs", "_Right", "_Left", "_Top", "_Bottom", "_Width", "_Height", "withoutTransforms", "xPercent", "yPercent", "rotation", "rotationX", "rotationY", "scale", "skewX", "skewY", "getBoundingClientRect", "_markerDefaults", "_defaults", "toggleActions", "anticipatePin", "center", "bottom", "right", "flipped", "side", "oppositeSide", "_isFlipped", "_ids", "_listeners", "_emptyArray", "map", "_refreshID", "<PERSON><PERSON><PERSON><PERSON>", "isRefreshing", "refreshInits", "scroll<PERSON>eh<PERSON>or", "refresh", "_subPinOffset", "horizontal", "original", "adjustPinSpacing", "_dir", "_endClamp", "setPositions", "render", "onRefresh", "_lastScroll", "_direction", "isUpdating", "recordVelocity", "concat", "_capsExp", "replace", "toLowerCase", "tweenTo", "pinCache", "snapFunc", "scroll1", "scroll2", "markerStart", "markerEnd", "markerStartTrigger", "markerEndTrigger", "markerVars", "executingOnRefresh", "change", "pinOriginalState", "pinActiveState", "pinState", "pinGetter", "pinSetter", "pinStart", "pinChange", "spacingStart", "markerStartSetter", "pinMoves", "markerEndSetter", "snap1", "snap2", "scrubTween", "scrubSmooth", "snapDurClamp", "snapDelayedCall", "prevScroll", "prevAnimProgress", "caMarkerSetter", "customRevertReturn", "nodeType", "toggleClass", "onToggle", "scrub", "pinSpacing", "invalidateOnRefresh", "onScrubComplete", "onSnapComplete", "once", "pinReparent", "pinSpacer", "fastScrollEnd", "preventOverlaps", "isToggle", "scrollerCache", "pinType", "callbacks", "onEnter", "onLeave", "onEnterBack", "onLeaveBack", "markers", "onRefreshInit", "getScrollerSize", "_getSizeFunc", "getScrollerOffsets", "_getOffsetsFunc", "lastSnap", "lastRefresh", "prevProgress", "_startClamp", "bind", "refreshPriority", "tweenScroll", "scrubDuration", "ease", "totalProgress", "paused", "lazy", "_initted", "isReverted", "immediateRender", "snapTo", "_getClosestLabel", "_getLabelAtDirection", "st", "directional", "delay", "refreshedRecently", "isActive", "velocity", "naturalEnd", "inertia", "endValue", "endScroll", "onStart", "stRevert", "targets", "className", "nativeElement", "spacerIsNative", "classList", "add", "force3D", "quickSetter", "content", "_makePositionable", "oldOnUpdate", "oldParams", "onUpdateParams", "eventCallback", "apply", "previous", "next", "temp", "r", "prevRefreshing", "_swapPinOut", "soft", "pinOffset", "invalidate", "isVertical", "override", "curTrigger", "curPin", "oppositeScroll", "initted", "revertedPins", "forcedOverflow", "markerStartOffset", "markerEndOffset", "isFirstRefresh", "otherPinOffset", "parsedEnd", "parsedEndTrigger", "endTrigger", "parsedStart", "<PERSON><PERSON><PERSON><PERSON>", "triggerIndex", "unshift", "_pinPush", "normalize", "toUpperCase", "ceil", "_copyState", "omitOffsets", "_pinOffset", "endAnimation", "labelToScroll", "label", "getTrailing", "reverse", "forceFake", "toggleState", "action", "stateChanged", "toggled", "isAtMax", "isTakingAction", "clipped", "_dp", "_time", "_start", "resetTo", "_tTime", "_tDur", "n", "newStart", "newEnd", "keepClamp", "amount", "allowAnimation", "onKill", "updateFunc", "_queueRefreshAll", "clearInterval", "suppressOverwrites", "_rafBugFix", "userAgent", "mm", "bodyStyle", "border", "borderTopStyle", "AnimationProto", "Animation", "prototype", "Object", "defineProperty", "setInterval", "checkPrefix", "w", "h", "hidden", "limitCallbacks", "ms", "syncInterval", "ignoreMobileResize", "autoRefreshEvents", "scrollerProxy", "clearMatchMedia", "isInViewport", "positionInViewport", "referencePoint", "killAll", "allowListeners", "listeners", "saveStyles", "getAttribute", "safe", "clearScrollMemory", "maxScroll", "getScrollFunc", "isScrolling", "snapDirectional", "batch", "proxyCallback", "elements", "triggers", "interval", "batchMax", "varsCopy", "_clampScrollAndGetDurationMultiplier", "_allowNativePanning", "touchAction", "_nestedScroll", "node", "_isScrollT", "scrollHeight", "clientHeight", "scrollWidth", "_overflow", "overflowY", "overflowX", "_isScroll", "stopPropagation", "_inputObserver", "inputs", "nested", "_captureInputs", "_getScrollNormalizer", "resumeTouchMove", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updateClamps", "maxY", "scrollClampY", "normalizeScrollX", "scrollClampX", "lastRefreshID", "removeContentOffset", "transform", "onResize", "startScrollX", "startScrollY", "momentum", "allowNestedScroll", "smoother", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smootherInstance", "get", "initialScale", "visualViewport", "outerWidth", "wheelRefresh", "resolveMomentumDuration", "inputObserver", "ignoreDrag", "prevScale", "currentScroll", "dur", "velocityX", "velocityY", "play", "_ts", "xArray", "yArray", "yClamped", "ticker", "_inputIsFocused", "auto", "_inputExp", "isInput", "tagName", "observe", "normalizeScroll", "normalizer", "ss", "ref"], "mappings": ";;;;;;;;;mYAYY,SAAXA,WAAiBC,IAA4B,oBAAZC,SAA4BD,GAAOC,OAAOD,OAASA,GAAKE,gBAAkBF,GAkB3F,SAAhBG,EAAiBC,EAASC,UAAcC,GAASC,QAAQH,IAAYE,GAASA,GAASC,QAAQH,GAAW,GAAGC,GAC/F,SAAdG,EAAcC,YAASC,EAAMH,QAAQE,GACtB,SAAfE,EAAgBP,EAASQ,EAAMC,EAAMC,EAAYC,UAAYX,EAAQY,iBAAiBJ,EAAMC,EAAM,CAACI,SAAUH,EAAYC,UAAWA,IAClH,SAAlBG,EAAmBd,EAASQ,EAAMC,EAAME,UAAYX,EAAQe,oBAAoBP,EAAMC,IAAQE,GAGlF,SAAZK,WAAmBC,IAAeA,GAAYC,WAAcC,GAAWC,QACpD,SAAnBC,EAAoBC,EAAGC,GACJ,SAAdC,GAAcC,MACbA,GAAmB,IAAVA,EAAa,CACzBC,IAAaC,GAAKC,QAAQC,kBAAoB,cAC1CC,EAAgBb,IAAeA,GAAYC,UAC/CO,EAAQD,GAAYO,EAAIC,KAAKC,MAAMR,KAAWR,IAAeA,GAAYiB,IAAM,EAAI,GACnFZ,EAAEG,GACFD,GAAYW,QAAUhB,GAAWC,MACjCU,GAAiBM,EAAQ,KAAMX,QACrBF,GAAcJ,GAAWC,QAAUI,GAAYW,SAAWC,EAAQ,UAC5EZ,GAAYW,QAAUhB,GAAWC,MACjCI,GAAYO,EAAIT,YAEVE,GAAYO,EAAIP,GAAYa,cAEpCb,GAAYa,OAAS,EACdf,GAAKE,GAIA,SAAbc,EAAcC,EAAGC,UAAWA,GAAQA,EAAKC,MAAQD,EAAKC,KAAKC,UAAa9C,GAAK+C,MAAMC,SAASL,GAAG,KAAqB,iBAAPA,IAAoD,IAAjC3C,GAAKiD,SAASC,eAA2BC,QAAQC,KAAK,qBAAsBT,GAAK,MAEhM,SAAjBU,EAAkBjD,SAAUkD,IAAAA,EAAGC,IAAAA,GAC9B/C,EAAYJ,KAAaA,EAAUoD,GAAKC,kBAAoBC,QACxDC,EAAIpC,GAAWhB,QAAQH,GAC1BqC,EAASc,IAAOK,GAAUL,GAAK,EAAI,GAClCI,IAAMA,EAAIpC,GAAWsC,KAAKzD,GAAW,GACvCmB,GAAWoC,EAAIlB,IAAW9B,EAAaP,EAAS,SAAUgB,OACtD0C,EAAOvC,GAAWoC,EAAIlB,GACzB5B,EAAOiD,IAASvC,GAAWoC,EAAIlB,GAAUhB,EAAiBtB,EAAcC,EAASkD,IAAI,KAAU9C,EAAYJ,GAAWmD,EAAK9B,EAAiB,SAASI,UAAgBkC,UAAUC,OAAU5D,EAAQkD,GAAKzB,EAASzB,EAAQkD,cACxNzC,EAAKoD,OAAS7D,EACd0D,IAASjD,EAAKqD,OAAyD,WAAhDlE,GAAKmE,YAAY/D,EAAS,mBAC1CS,EAEW,SAAnBuD,EAAoBvC,EAAOwC,EAAgBC,GAOhC,SAATC,GAAU1C,EAAO2C,OACZ7B,EAAI8B,KACJD,GAAkBE,EAAT/B,EAAIgC,GAChBC,EAAKC,EACLA,EAAKhD,EACLiD,EAAKH,EACLA,EAAKhC,GACK2B,EACVO,GAAMhD,EAENgD,EAAKD,GAAM/C,EAAQ+C,IAAOjC,EAAImC,IAAOH,EAAKG,OAhBzCD,EAAKhD,EACR+C,EAAK/C,EACL8C,EAAKF,KACLK,EAAKH,EACLD,EAAML,GAAkB,GACxBU,EAAiB3C,KAAK4C,IAAI,IAAW,EAANN,SAsBzB,CAACH,OAAAA,GAAQU,MARP,SAARA,QAAgBL,EAAKC,EAAKP,EAAW,EAAIO,EAAIC,EAAKH,EAAK,GAQjCO,YAPR,SAAdA,YAAcC,OACTC,EAAON,EACVO,EAAOT,EACPjC,EAAI8B,YACJU,GAA+B,IAAhBA,GAAsBA,IAAgBN,GAAMN,GAAOY,GAC3DR,IAAOG,GAAeC,EAATpC,EAAImC,EAAuB,GAAKD,GAAMP,EAAWe,GAAQA,MAAWf,EAAW3B,EAAIgC,GAAMS,GAAQ,MAI7G,SAAZE,EAAaC,EAAGC,UACfA,IAAmBD,EAAEE,YAAcF,EAAEC,iBAC9BD,EAAEG,eAAiBH,EAAEG,eAAe,GAAKH,EAE/B,SAAlBI,EAAkBC,OACbZ,EAAM5C,KAAK4C,UAAL5C,KAAYwD,GACrBlB,EAAMtC,KAAKsC,UAALtC,KAAYwD,UACZxD,KAAKyD,IAAIb,IAAQ5C,KAAKyD,IAAInB,GAAOM,EAAMN,EAE3B,SAApBoB,KACCC,GAAgB/F,GAAKgG,KAAKC,UAAUF,gBACnBA,GAAcC,MA7FnB,SAAbE,iBACKF,EAAOD,GAAcC,KACxBG,EAAOH,EAAKI,QAAU,GACtBC,EAAYL,EAAKzE,WACjB+E,EAAUN,EAAK1F,SAChB+F,EAAUxC,WAAVwC,EAAkB9E,IAClB+E,EAAQzC,WAARyC,EAAgBhG,IAChBiB,GAAa8E,EACb/F,GAAWgG,EACX9D,EAAU,iBAAC+D,EAAM1E,UAAUsE,EAAKI,GAAM1E,IAoFCqE,GAE5B,SAAZM,EAAYR,UACXhG,GAAOgG,GAAQjG,MACkB,oBAAd0G,UAA6BA,SAASC,OACxD3E,GAAO9B,OAEPyD,IADAF,GAAOiD,UACOE,gBACdC,GAAQpD,GAAKkD,KACbhG,EAAQ,CAACqB,GAAMyB,GAAME,GAAQkD,IACpB5G,GAAK+C,MAAM8D,MACpBC,GAAW9G,GAAKgG,KAAKe,SAAW,aAChCC,GAAe,mBAAoBJ,GAAQ,UAAY,QAEvDK,GAAWC,EAASC,QAAUpF,GAAKqF,YAAcrF,GAAKqF,WAAW,oCAAoCC,QAAU,EAAK,iBAAkBtF,IAAmC,EAA3BuF,UAAUC,gBAAmD,EAA7BD,UAAUE,iBAAwB,EAAI,EACpNC,GAAcP,EAASQ,YAAc,iBAAkBhE,GAAS,4CAAgD,kBAAmBA,GAAkD,kDAAxC,uCAA2FiE,MAAM,KAC9OC,WAAW,kBAAM9F,EAAW,GAAG,KAC/BgE,IACA+B,GAAe,GAETA,GAzHT,IAAI7H,GAAM6H,GAAsB9F,GAAMyB,GAAME,GAAQkD,GAAOK,GAAUD,GAAcjB,GAAerF,EAAOW,GAAaoG,GAAaX,GAElIhF,EAAW,EACXgG,GAAa,GACbvG,GAAa,GACbjB,GAAW,GACXmE,GAAWsD,KAAKC,IAChBxF,EAAU,iBAAC+D,EAAM1E,UAAUA,GAgB3BoG,EAAc,aACdC,EAAa,YAoBbC,GAAc,CAAC7E,EAAG2E,EAAaG,EAAG,OAAQC,GAAI,OAAQC,GAAI,QAASC,IAAK,QAASC,EAAG,QAASC,GAAI,QAAS7C,EAAG,IAAKrC,GAAI9B,EAAiB,SAASI,UAAgBkC,UAAUC,OAASjC,GAAK2G,SAAS7G,EAAO+B,GAAUL,MAAQxB,GAAK4G,aAAenF,GAAKyE,IAAgBvE,GAAOuE,IAAgBrB,GAAMqB,IAAgB,KAChTrE,GAAY,CAACN,EAAG4E,EAAYE,EAAG,MAAOC,GAAI,MAAOC,GAAI,SAAUC,IAAK,SAAUC,EAAG,SAAUC,GAAI,SAAU7C,EAAG,IAAKgD,GAAIT,GAAa5E,GAAI9B,EAAiB,SAASI,UAAgBkC,UAAUC,OAASjC,GAAK2G,SAASP,GAAY5E,KAAM1B,GAASE,GAAK8G,aAAerF,GAAK0E,IAAexE,GAAOwE,IAAetB,GAAMsB,IAAe,KA+EhUC,GAAYS,GAAKhF,GACjBrC,GAAWC,MAAQ,MAEN0F,sBAKZ4B,KAAA,cAAKC,GACJlB,IAAgBrB,EAAUxG,KAASmD,QAAQC,KAAK,wCAChD2C,IAAiBD,QACZkD,EAA6bD,EAA7bC,UAAWC,EAAkbF,EAAlbE,YAAarI,EAAqamI,EAAranI,KAAMqD,EAA+Z8E,EAA/Z9E,OAAQiF,EAAuZH,EAAvZG,WAAYC,EAA2YJ,EAA3YI,SAAU3D,EAAiYuD,EAAjYvD,eAAgB4D,EAAiXL,EAAjXK,OAAQC,EAAyWN,EAAzWM,YAAaC,EAA4VP,EAA5VO,OAAQC,EAAoVR,EAApVQ,WAAYC,EAAwUT,EAAxUS,MAAOC,EAAiUV,EAAjUU,YAAaC,EAAoTX,EAApTW,UAAWC,EAAySZ,EAAzSY,OAAQC,EAAiSb,EAAjSa,QAASC,EAAwRd,EAAxRc,UAAWC,EAA6Qf,EAA7Qe,QAASC,EAAoQhB,EAApQgB,OAAQC,EAA4PjB,EAA5PiB,KAAMC,EAAsPlB,EAAtPkB,OAAQC,EAA8OnB,EAA9OmB,UAAWC,EAAmOpB,EAAnOoB,UAAWC,EAAwNrB,EAAxNqB,SAAUC,EAA8MtB,EAA9MsB,UAAWC,EAAmMvB,EAAnMuB,UAAWC,EAAwLxB,EAAxLwB,QAASC,EAA+KzB,EAA/KyB,WAAYC,EAAmK1B,EAAnK0B,OAAQC,EAA2J3B,EAA3J2B,YAAaC,EAA8I5B,EAA9I4B,aAAcC,EAAgI7B,EAAhI6B,eAAgBC,EAAgH9B,EAAhH8B,aAAcC,EAAkG/B,EAAlG+B,QAASC,EAAyFhC,EAAzFgC,SAAUC,EAA+EjC,EAA/EiC,UAAWC,EAAoElC,EAApEkC,QAASC,EAA2DnC,EAA3DmC,YAAanK,EAA8CgI,EAA9ChI,QAASoK,EAAqCpC,EAArCoC,YAAaC,EAAwBrC,EAAxBqC,SAAUC,EAActC,EAAdsC,WAyBpa,SAAfC,YAAqBC,GAAc9G,KACpB,SAAf+G,GAAgBjG,EAAGkG,UAAsB7I,GAAK4G,MAAQjE,IAAO+D,IAAWA,EAAO/I,QAAQgF,EAAEtB,SAAawH,GAAoBC,IAAkC,UAAlBnG,EAAEoG,aAA6BjB,GAAeA,EAAYnF,EAAGkG,GAO9L,SAATlH,SACKqH,EAAKhJ,GAAKiJ,OAASlG,EAAgBkG,IACtCC,EAAKlJ,GAAKmJ,OAASpG,EAAgBoG,IACnCC,EAAW5J,KAAKyD,IAAI+F,IAAO5C,EAC3BiD,EAAW7J,KAAKyD,IAAIiG,IAAO9C,EAC5BoB,IAAa4B,GAAYC,IAAa7B,EAASxH,GAAMgJ,EAAIE,EAAID,GAAQE,IACjEC,IACHlC,GAAyB,EAAdlH,GAAKiJ,QAAc/B,EAAQlH,IACtCmH,GAAUnH,GAAKiJ,OAAS,GAAK9B,EAAOnH,IACpCsH,GAAaA,EAAUtH,IACvByH,GAAezH,GAAKiJ,OAAS,GAAQK,GAAa,GAAO7B,EAAUzH,IACnEsJ,GAAatJ,GAAKiJ,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,GAEjCI,IACHhC,GAAwB,EAAdrH,GAAKmJ,QAAc9B,EAAOrH,IACpCoH,GAAQpH,GAAKmJ,OAAS,GAAK/B,EAAKpH,IAChCuH,GAAaA,EAAUvH,IACvB0H,GAAe1H,GAAKmJ,OAAS,GAAQI,GAAa,GAAO7B,EAAU1H,IACnEuJ,GAAavJ,GAAKmJ,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,IAEjCK,IAASC,MACZ5B,GAAUA,EAAO7H,IACbyJ,KACH1C,EAAO/G,IACPyJ,IAAU,GAEXD,IAAQ,GAETE,MAAYA,IAAS,IAAUjB,GAAcA,EAAWzI,IACpD2J,KACHzB,EAAQlI,IACR2J,IAAU,GAEXC,GAAK,EAEI,SAAVC,GAAWC,EAAGC,EAAGC,GAChBf,GAAOe,IAAUF,EACjBX,GAAOa,IAAUD,EACjB/J,GAAKiK,IAAItI,OAAOmI,GAChB9J,GAAKkK,IAAIvI,OAAOoI,GAChBxD,EAAkBqD,GAAPA,IAAYO,sBAAsBxI,IAAWA,KAEjC,SAAxByI,GAAyBN,EAAGC,GACvBvB,IAAa6B,KAChBrK,GAAKqK,KAAOA,GAAO7K,KAAKyD,IAAI6G,GAAKtK,KAAKyD,IAAI8G,GAAK,IAAM,IACrDL,IAAS,GAEG,MAATW,KACHpB,GAAO,IAAMa,EACb9J,GAAKiK,IAAItI,OAAOmI,GAAG,IAEP,MAATO,KACHlB,GAAO,IAAMY,EACb/J,GAAKkK,IAAIvI,OAAOoI,GAAG,IAEpBxD,EAAkBqD,GAAPA,IAAYO,sBAAsBxI,IAAWA,KAE/C,SAAV2I,GAAU3H,OACLiG,GAAajG,EAAG,QAEhBmH,GADJnH,EAAID,EAAUC,EAAGC,IACP2H,QACTR,EAAIpH,EAAE6H,QACNxB,EAAKc,EAAI9J,GAAK8J,EACdZ,EAAKa,EAAI/J,GAAK+J,EACdU,EAAazK,GAAKyK,WACnBzK,GAAK8J,EAAIA,EACT9J,GAAK+J,EAAIA,GACLU,GAAcjL,KAAKyD,IAAIjD,GAAK0K,OAASZ,IAAMzD,GAAe7G,KAAKyD,IAAIjD,GAAK2K,OAASZ,IAAM1D,KAC1FU,IAAW0C,IAAU,GACrBgB,IAAezK,GAAKyK,YAAa,GACjCL,GAAsBpB,EAAIE,GAC1BuB,GAAc5D,GAAeA,EAAY7G,MA8CzB,SAAlB4K,GAAkBjI,UAAKA,EAAEkI,SAA8B,EAAnBlI,EAAEkI,QAAQzJ,SAAepB,GAAK8K,aAAc,IAAS9C,EAAerF,EAAG3C,GAAKyK,YAChG,SAAhBM,YAAuB/K,GAAK8K,aAAc,IAAU7C,EAAajI,IACtD,SAAXgL,GAAWrI,OACNiG,GAAajG,QACbmH,EAAImB,KACPlB,EAAImB,KACLrB,IAASC,EAAIqB,IAAW7C,GAAcyB,EAAIqB,IAAW9C,EAAa,GAClE6C,GAAUrB,EACVsB,GAAUrB,EACVvD,GAAU6E,GAAkBC,SAAQ,IAE1B,SAAXC,GAAW5I,OACNiG,GAAajG,IACjBA,EAAID,EAAUC,EAAGC,GACjBsF,IAAYyB,IAAU,OAClB6B,GAA8B,IAAhB7I,EAAE8I,UAAkBnF,EAA6B,IAAhB3D,EAAE8I,UAAkBtM,GAAKuM,YAAc,GAAK/E,EAC/FkD,GAAQlH,EAAEsG,OAASuC,EAAY7I,EAAEwG,OAASqC,EAAY,GACtDhF,IAAWuB,GAAgBsD,GAAkBC,SAAQ,IAE5C,SAAVK,GAAUhJ,OACLiG,GAAajG,QACbmH,EAAInH,EAAE4H,QACTR,EAAIpH,EAAE6H,QACNxB,EAAKc,EAAI9J,GAAK8J,EACdZ,EAAKa,EAAI/J,GAAK+J,EACf/J,GAAK8J,EAAIA,EACT9J,GAAK+J,EAAIA,EACTP,IAAQ,GACPR,GAAME,IAAOkB,GAAsBpB,EAAIE,IAE9B,SAAX0C,GAAWjJ,GAAM3C,GAAK4G,MAAQjE,EAAGgF,EAAQ3H,IAC3B,SAAd6L,GAAclJ,GAAM3C,GAAK4G,MAAQjE,EAAGiF,EAAW5H,IACpC,SAAX8L,GAAWnJ,UAAKiG,GAAajG,IAAOD,EAAUC,EAAGC,IAAmByF,EAAQrI,SAvLxEqB,OAASA,EAASvB,EAAWuB,IAAWP,QACxCqF,KAAOA,EACDO,EAAXA,GAAoBtJ,GAAK+C,MAAMC,QAAQsG,GACvCN,EAAYA,GAAa,KACzBC,EAAcA,GAAe,EAC7BM,EAAaA,GAAc,EAC3B2B,EAAcA,GAAe,EAC7BtK,EAAOA,GAAQ,sBACfuI,GAAwB,IAAbA,EACID,EAAfA,GAA4ByF,WAAW5M,GAAK6M,iBAAiBhI,IAAOsC,aAAe,OAC/EsD,GAAIyB,GAAmB5B,GAASD,GAAOG,GAASD,GAAQW,GAC3DrK,GAAOiM,KACP3C,GAAa,EACbC,GAAa,EACb0B,GAAcxK,EAAeY,EAAQkE,IACrC2F,GAAczK,EAAeY,EAAQL,IACrCmK,GAAUF,KACVG,GAAUF,KACVpC,IAAgB9K,EAAKL,QAAQ,YAAcK,EAAKL,QAAQ,YAAiC,gBAAnBkH,GAAY,GAClFqH,GAAatO,EAAYyD,GACzB8K,GAAW9K,EAAO+K,eAAiBxL,GACnCqI,GAAS,CAAC,EAAG,EAAG,GAChBE,GAAS,CAAC,EAAG,EAAG,GAChBR,GAAc,EAqFd0D,GAAWrM,GAAKgH,QAAU,SAAArE,GACrBiG,GAAajG,EAAG,IAAOA,GAAKA,EAAE2J,SAClCtM,GAAKqK,KAAOA,GAAO,KACnBgB,GAAkBkB,QAClBvM,GAAKtB,WAAY,EACjBiE,EAAID,EAAUC,GACd2G,GAAaC,GAAa,EAC1BvJ,GAAK0K,OAAS1K,GAAK8J,EAAInH,EAAE4H,QACzBvK,GAAK2K,OAAS3K,GAAK+J,EAAIpH,EAAE6H,QACzBxK,GAAKiK,IAAI5H,QACTrC,GAAKkK,IAAI7H,QACTtE,EAAagK,EAAe1G,EAAS8K,GAAUtH,GAAY,GAAIyF,GAAS1H,GAAgB,GACxF5C,GAAKiJ,OAASjJ,GAAKmJ,OAAS,EAC5BnC,GAAWA,EAAQhH,MAEpBwM,GAAaxM,GAAKiH,UAAY,SAAAtE,OACzBiG,GAAajG,EAAG,IACpBrE,EAAgByJ,EAAe1G,EAAS8K,GAAUtH,GAAY,GAAIyF,IAAS,OACvEmC,GAAkBC,MAAM1M,GAAK+J,EAAI/J,GAAK2K,QACzCgC,EAAc3M,GAAKyK,aAAgD,EAAjCjL,KAAKyD,IAAIjD,GAAK8J,EAAI9J,GAAK0K,SAAgD,EAAjClL,KAAKyD,IAAIjD,GAAK+J,EAAI/J,GAAK2K,SAC/FiC,EAAYlK,EAAUC,IAClBgK,GAAeF,IACnBzM,GAAKiK,IAAI5H,QACTrC,GAAKkK,IAAI7H,QACLO,GAAkB2F,GACrBnL,GAAKyP,YAAY,IAAM,cACS,IAA3BhL,KAAa8G,KAAsBhG,EAAEmK,oBACpCnK,EAAEtB,OAAO0L,MACZpK,EAAEtB,OAAO0L,aACH,GAAIZ,GAASa,YAAa,KAC5BC,EAAiBd,GAASa,YAAY,eAC1CC,EAAeC,eAAe,SAAS,GAAM,EAAM/N,GAAM,EAAGyN,EAAUO,QAASP,EAAUQ,QAASR,EAAUrC,QAASqC,EAAUpC,SAAS,GAAO,GAAO,GAAO,EAAO,EAAG,MACvK7H,EAAEtB,OAAOgM,cAAcJ,OAM5BjN,GAAKyK,WAAazK,GAAK8K,YAAc9K,GAAKtB,WAAY,EACtD8H,IAAWuB,GAAgBsD,GAAkBC,SAAQ,GACrDxE,GAAa6F,GAAe7F,EAAU9G,IACtCiH,GAAaA,EAAUjH,GAAM2M,KAoC/BtB,GAAoBrL,GAAKsN,IAAMlQ,GAAKyP,YAAYpG,GAAe,IA/JjD,SAAb8G,aACCvN,GAAKiK,IAAI5H,QACTrC,GAAKkK,IAAI7H,QACTgJ,GAAkBkB,QAClB/F,GAAUA,EAAOxG,MA2J8DuM,QAEjFvM,GAAKiJ,OAASjJ,GAAKmJ,OAAS,EAC5BnJ,GAAKiK,IAAMzI,EAAiB,EAAG,IAAI,GACnCxB,GAAKkK,IAAM1I,EAAiB,EAAG,IAAI,GACnCxB,GAAKmL,QAAUF,GACfjL,GAAKoL,QAAUF,GACflL,GAAKyK,WAAazK,GAAK8K,YAAc9K,GAAKtB,WAAY,EACtDwF,GAAS+H,MACTjM,GAAKwN,OAAS,SAAA7K,UACR3C,GAAKyN,YACT1P,EAAamO,GAAaC,GAAW9K,EAAQ,SAAU7C,GAC7B,GAA1BR,EAAKL,QAAQ,WAAkBI,EAAamO,GAAaC,GAAW9K,EAAQ,SAAU2J,GAAUpI,EAAgBzE,GACvF,GAAzBH,EAAKL,QAAQ,UAAiBI,EAAasD,EAAQ,QAASkK,GAAU3I,EAAgBzE,IACxD,GAAzBH,EAAKL,QAAQ,UAAiB0G,IAAwC,GAA3BrG,EAAKL,QAAQ,cAC5DI,EAAasD,EAAQwD,GAAY,GAAIwH,GAAUzJ,EAAgBzE,GAC/DJ,EAAaoO,GAAUtH,GAAY,GAAI2H,IACvCzO,EAAaoO,GAAUtH,GAAY,GAAI2H,IACvCjE,GAAexK,EAAasD,EAAQ,QAASqH,IAAc,GAAO,GAClEL,GAAWtK,EAAasD,EAAQ,QAASyK,IACzC9D,GAAkBjK,EAAaoO,GAAU,eAAgBvB,IACzD3C,GAAgBlK,EAAaoO,GAAU,aAAcpB,IACrDpD,GAAW5J,EAAasD,EAAQ+C,GAAe,QAASwH,IACxDhE,GAAc7J,EAAasD,EAAQ+C,GAAe,QAASyH,IAC3DhE,GAAU9J,EAAasD,EAAQ+C,GAAe,OAAQuH,KAEvD3L,GAAKyN,WAAY,EACjB9K,GAAKA,EAAE3E,MAAQqO,GAAS1J,GACxBwF,GAAYA,EAASnI,KAEfA,IAERA,GAAK0N,QAAU,WACV1N,GAAKyN,YAERvI,GAAWyI,OAAO,SAAAC,UAAKA,IAAM5N,IAAQpC,EAAYgQ,EAAEvM,UAASD,QAAU9C,EAAgB4N,GAAaC,GAAW9K,EAAQ,SAAU7C,GAC5HwB,GAAKtB,YACRsB,GAAKiK,IAAI5H,QACTrC,GAAKkK,IAAI7H,QACT/D,EAAgByJ,EAAe1G,EAAS8K,GAAUtH,GAAY,GAAIyF,IAAS,IAE5EhM,EAAgB4N,GAAaC,GAAW9K,EAAQ,SAAU2J,GAAU7M,GACpEG,EAAgB+C,EAAQ,QAASkK,GAAUpN,GAC3CG,EAAgB+C,EAAQwD,GAAY,GAAIwH,GAAUlO,GAClDG,EAAgB6N,GAAUtH,GAAY,GAAI2H,IAC1ClO,EAAgB6N,GAAUtH,GAAY,GAAI2H,IAC1ClO,EAAgB+C,EAAQ,QAASqH,IAAc,GAC/CpK,EAAgB+C,EAAQ,QAASyK,IACjCxN,EAAgB6N,GAAU,eAAgBvB,IAC1CtM,EAAgB6N,GAAU,aAAcpB,IACxCzM,EAAgB+C,EAAQ+C,GAAe,QAASwH,IAChDtN,EAAgB+C,EAAQ+C,GAAe,QAASyH,IAChDvN,EAAgB+C,EAAQ+C,GAAe,OAAQuH,IAC/C3L,GAAKyN,UAAYzN,GAAKtB,UAAYsB,GAAKyK,YAAa,EACpDrC,GAAaA,EAAUpI,MAIzBA,GAAK6N,KAAO7N,GAAK8N,OAAS,WACzB9N,GAAK0N,cACD3M,EAAImE,GAAWvH,QAAQqC,IACtB,GAALe,GAAUmE,GAAW6I,OAAOhN,EAAG,GAC/BtC,KAAgBuB,KAASvB,GAAc,IAGxCyG,GAAWjE,KAAKjB,IAChB+H,GAAgBnK,EAAYyD,KAAY5C,GAAcuB,IAEtDA,GAAKwN,OAAO5G,8JAILqF,KAAKhC,IAAI3H,2DAGT2J,KAAK/B,IAAI5H,8CA5QL6D,QACND,KAAKC,GAgRZ7B,EAAS0J,QAAU,SACnB1J,EAAS2J,OAAS,SAAA9H,UAAQ,IAAI7B,EAAS6B,IACvC7B,EAAS4J,SAAWtK,EACpBU,EAAS6J,OAAS,kBAAMjJ,GAAWkJ,SACnC9J,EAAS+J,QAAU,SAAAzE,UAAM1E,GAAWyI,OAAO,SAAAC,UAAKA,EAAEzH,KAAKyD,KAAOA,IAAI,IAElEzM,KAAcC,GAAKE,eAAegH,GC9YnB,SAAdgK,GAAerP,EAAOjB,EAAMgC,OACvBiE,EAASsK,GAAUtP,KAAkC,WAAvBA,EAAMuP,OAAO,EAAG,KAA2C,EAAxBvP,EAAMtB,QAAQ,eACnFqC,EAAK,IAAMhC,EAAO,SAAWiG,GACdhF,EAAMuP,OAAO,EAAGvP,EAAMmC,OAAS,GAAKnC,EAEvC,SAAbwP,GAAcxP,EAAOgF,UAAUA,GAAWsK,GAAUtP,IAAiC,WAAvBA,EAAMuP,OAAO,EAAG,GAA4CvP,EAAzB,SAAWA,EAAQ,IAE9F,SAAtByP,YAA4BC,GAAiB,EACzB,SAApBC,YAA0BD,GAAiB,EAC5B,SAAfE,GAAetP,UAAKA,EACX,SAATuP,GAAS7P,UAASO,KAAKC,MAAc,IAARR,GAAkB,KAAU,EACzC,SAAhB8P,WAAyC,oBAAZ1R,OAClB,SAAXF,YAAiBC,IAAS2R,OAAoB3R,GAAOC,OAAOD,OAASA,GAAKE,gBAAkBF,GAC9E,SAAdQ,GAAc+E,YAAQ7E,EAAMH,QAAQgF,GACZ,SAAxBqM,GAAwBC,UAA4C,WAAtBA,EAAiCC,EAAS/P,GAAK,QAAU8P,KAAuBnO,GAAO,SAAWmO,IAAsBjL,GAAM,SAAWiL,GACtK,SAAjBE,GAAiB3R,UAAWD,EAAcC,EAAS,2BAA6BI,GAAYJ,GAAW,kBAAO4R,GAAYC,MAAQlQ,GAAKmQ,WAAYF,GAAYG,OAASL,EAAeE,IAAgB,kBAAMI,GAAWhS,KAG3M,SAAbiS,GAAcjS,SAAUkD,IAAAA,EAAGmF,IAAAA,GAAID,IAAAA,EAAG5C,IAAAA,SAAOxD,KAAK4C,IAAI,GAAI1B,EAAI,SAAWmF,KAAQ7C,EAAIzF,EAAcC,EAASkD,IAAMsC,IAAMmM,GAAe3R,EAAf2R,GAA0BvJ,GAAKhI,GAAYJ,IAAYsD,GAAOJ,IAAMsD,GAAMtD,IAAMsO,GAAsBnJ,GAAMrI,EAAQkD,GAAKlD,EAAQ,SAAWqI,IAC1O,SAAtB6J,GAAuBzR,EAAM0R,OACvB,IAAI5O,EAAI,EAAGA,EAAI6O,EAAaxO,OAAQL,GAAK,EAC3C4O,KAAWA,EAAOhS,QAAQiS,EAAa7O,EAAE,KAAQ9C,EAAK2R,EAAa7O,GAAI6O,EAAa7O,EAAE,GAAI6O,EAAa7O,EAAE,IAI/F,SAAd8O,GAAc5Q,SAA2B,mBAAXA,EAClB,SAAZ6Q,GAAY7Q,SAA2B,iBAAXA,EAChB,SAAZ8Q,GAAY9Q,SAA2B,iBAAXA,EACZ,SAAhB+Q,GAAiBC,EAAWC,EAAU3D,UAAU0D,GAAaA,EAAUE,SAASD,EAAW,EAAI,IAAM3D,GAAS0D,EAAU1D,QAC5G,SAAZ6D,GAAapQ,EAAM/B,MACd+B,EAAKqQ,QAAS,KACbC,EAASrS,EAAK+B,GAClBsQ,GAAUA,EAAOC,YAAcvQ,EAAKwQ,kBAAoBF,IAmBtC,SAApBG,GAAoBjT,UAAW2B,GAAK6M,iBAAiBxO,GAKtC,SAAfkT,GAAgBC,EAAKC,OACf,IAAIpL,KAAKoL,EACZpL,KAAKmL,IAASA,EAAInL,GAAKoL,EAASpL,WAE3BmL,EAQG,SAAXE,GAAYrT,SAAUqI,IAAAA,UAAQrI,EAAQ,SAAWqI,IAAOrI,EAAQ,SAAWqI,IAAO,EAC5D,SAAtBiL,GAAsBC,OAIpBvL,EAHGxC,EAAI,GACPgO,EAASD,EAASC,OAClBC,EAAWF,EAASE,eAEhBzL,KAAKwL,EACThO,EAAE/B,KAAK+P,EAAOxL,GAAKyL,UAEbjO,EAGW,SAAnBkO,GAAmBC,OACdC,EAAOhU,GAAK+C,MAAMiR,KAAKD,GAC1BnO,EAAIqO,MAAMC,QAAQH,IAAyBA,EAAqB/C,MAAM,GAAGmD,KAAK,SAACvO,EAAGwO,UAAMxO,EAAIwO,WACtFxO,EAAI,SAAC/D,EAAOwS,EAAWC,OACzB3Q,cADyB2Q,IAAAA,EAAW,OAEnCD,SACGL,EAAKnS,MAEG,EAAZwS,EAAe,KAClBxS,GAASyS,EACJ3Q,EAAI,EAAGA,EAAIiC,EAAE5B,OAAQL,OACrBiC,EAAEjC,IAAM9B,SACJ+D,EAAEjC,UAGJiC,EAAEjC,EAAE,OAEXA,EAAIiC,EAAE5B,OACNnC,GAASyS,EACF3Q,QACFiC,EAAEjC,IAAM9B,SACJ+D,EAAEjC,UAILiC,EAAE,IACN,SAAC/D,EAAOwS,EAAWC,YAAAA,IAAAA,EAAW,UAC7BC,EAAUP,EAAKnS,UACXwS,GAAajS,KAAKyD,IAAI0O,EAAU1S,GAASyS,GAAeC,EAAU1S,EAAQ,GAAOwS,EAAY,EAAKE,EAAUP,EAAKK,EAAY,EAAIxS,EAAQkS,EAAuBlS,EAAQkS,IAIjK,SAAjBS,GAAkB3T,EAAMT,EAASqU,EAAOC,UAAaD,EAAM9M,MAAM,KAAKgN,QAAQ,SAAA/T,UAAQC,EAAKT,EAASQ,EAAM8T,KAC3F,SAAf/T,GAAgBP,EAASQ,EAAMC,EAAMC,EAAYC,UAAYX,EAAQY,iBAAiBJ,EAAMC,EAAM,CAACI,SAAUH,EAAYC,UAAWA,IAClH,SAAlBG,GAAmBd,EAASQ,EAAMC,EAAME,UAAYX,EAAQe,oBAAoBP,EAAMC,IAAQE,GAC7E,SAAjB6T,GAAkB/T,EAAMJ,EAAIoU,IAC3BA,EAAaA,GAAcA,EAAWC,gBAErCjU,EAAKJ,EAAI,QAASoU,GAClBhU,EAAKJ,EAAI,YAAaoU,IAMV,SAAdE,GAAelT,EAAOmT,MACjB7D,GAAUtP,GAAQ,KACjBoT,EAAUpT,EAAMtB,QAAQ,KAC3B2U,GAAYD,GAAYpT,EAAMsT,OAAOF,EAAQ,GAAK,GAAKtG,WAAW9M,EAAMuP,OAAO6D,EAAU,IAAM,GAC3FA,IACHpT,EAAMtB,QAAQ,KAAO0U,IAAaC,GAAYF,EAAO,KACtDnT,EAAQA,EAAMuP,OAAO,EAAG6D,EAAQ,IAEjCpT,EAAQqT,GAAarT,KAASuT,EAAaA,EAAUvT,GAASmT,GAAQnT,EAAMtB,QAAQ,KAAOoO,WAAW9M,GAASmT,EAAO,IAAMrG,WAAW9M,IAAU,UAE3IA,EAEQ,SAAhBwT,GAAiBzU,EAAM2F,EAAM+O,EAAWjB,IAAiE5R,EAAQ8S,EAAcC,OAA3EC,IAAAA,WAAYC,IAAAA,SAAUC,IAAAA,SAAUC,IAAAA,OAAQC,IAAAA,WACvFtQ,EAAI/B,GAAKsS,cAAc,OAC1BC,EAAmBvV,GAAY8U,IAAsD,UAAxCnV,EAAcmV,EAAW,WACtEU,GAA2C,IAA9BpV,EAAKL,QAAQ,YAC1B0V,EAASF,EAAmBnP,GAAQ0O,EACpCY,GAAqC,IAA3BtV,EAAKL,QAAQ,SACvB4V,EAAQD,EAAUT,EAAaC,EAC/BU,EAAM,gBAAkBD,EAAQ,cAAgBR,EAAW,UAAYQ,EAAQ,gBAAkBN,EAAa,8IAC/GO,GAAO,cAAgBJ,GAAcR,IAAuBO,EAAmB,SAAW,cACzFC,IAAcR,GAAuBO,IAAsBK,IAAQ/B,IAAczQ,GAAYyS,EAASC,GAAW,KAAO7T,EAASkM,WAAWiH,IAAW,OACxJL,IAAiBa,GAAO,+CAAiDb,EAAagB,YAAc,OACpGhR,EAAEiR,SAAWN,EACb3Q,EAAEkR,aAAa,QAAS,eAAiB7V,GAAQ2F,EAAO,WAAaA,EAAO,KAC5EhB,EAAEmR,MAAMC,QAAUP,EAClB7Q,EAAEqR,UAAYrQ,GAAiB,IAATA,EAAa3F,EAAO,IAAM2F,EAAO3F,EACvDqV,EAAOY,SAAS,GAAKZ,EAAOa,aAAavR,EAAG0Q,EAAOY,SAAS,IAAMZ,EAAOc,YAAYxR,GACrFA,EAAEyR,QAAUzR,EAAE,SAAW8O,EAAUzL,GAAGH,IACtCwO,EAAgB1R,EAAG,EAAG8O,EAAW6B,GAC1B3Q,EAiBA,SAAR2R,YAA6C,GAA/BzS,KAAa0S,KAAoCC,EAAXA,GAAoBrK,sBAAsBsK,IAClF,SAAZjW,KACMC,GAAgBA,EAAYC,aAAaD,EAAYiM,OAAS1G,GAAM0Q,eACxE/V,GAAWC,QACPH,EACQ+V,EAAXA,GAAoBrK,sBAAsBsK,GAE1CA,IAEDF,IAAmBI,EAAU,eAC7BJ,GAAkB1S,MAGC,SAArB+S,KACCC,EAAmB1V,GAAKmQ,WACxBwF,EAAoB3V,GAAKuM,YAEd,SAAZqJ,KACCpW,GAAWC,QACVoW,IAAgBC,GAAkBrU,GAAKsU,mBAAsBtU,GAAKuU,yBAA6BC,GAAuBP,IAAqB1V,GAAKmQ,cAAc9P,KAAKyD,IAAI9D,GAAKuM,YAAcoJ,GAAwC,IAAnB3V,GAAKuM,cAAuB2J,EAAa/J,SAAQ,GAInP,SAAfgK,YAAqBhX,GAAgB6E,GAAe,YAAamS,KAAiBC,IAAY,GAG5E,SAAlBC,GAAkBC,OACZ,IAAI1U,EAAI,EAAGA,EAAI2U,EAAatU,OAAQL,GAAG,IACtC0U,GAASC,EAAa3U,EAAE,IAAM2U,EAAa3U,EAAE,GAAG4U,QAAUF,KAC9DC,EAAa3U,GAAG+S,MAAMC,QAAU2B,EAAa3U,EAAE,GAC/C2U,EAAa3U,GAAG6U,SAAWF,EAAa3U,GAAG8S,aAAa,YAAa6B,EAAa3U,EAAE,IAAM,IAC1F2U,EAAa3U,EAAE,GAAG8U,QAAU,GAIlB,SAAbC,GAAcjI,EAAM4H,OACfM,MACCC,GAAK,EAAGA,GAAKC,GAAU7U,OAAQ4U,OACnCD,EAAUE,GAAUD,MACHP,GAASM,EAAQ9V,OAASwV,IACtC5H,EACHkI,EAAQlI,KAAK,GAEbkI,EAAQjI,QAAO,GAAM,IAIxB2H,GAASD,GAAgBC,GACzBA,GAASd,EAAU,UAEC,SAArBuB,GAAsB7W,EAAmBuC,GACxCjD,GAAWC,SACVgD,GAAUuU,IAAmBxX,GAAWoT,QAAQ,SAAApB,UAAOd,GAAYc,IAAQA,EAAIhR,YAAcgR,EAAIyF,IAAM,KACxG7H,GAAUlP,KAAuBF,GAAKC,QAAQC,kBAAoBgX,EAAqBhX,GAWxE,SAAhBiX,KACCtS,GAAMmQ,YAAYoC,GAClBrH,EAASqH,EAAUC,cAAgBrX,GAAKuM,YACxC1H,GAAMyS,YAAYF,GAqGN,SAAbG,GAAcC,EAAKC,EAAQC,EAAIC,OACzBH,EAAII,MAAMC,UAAW,SAIxBxR,EAHGzE,EAAIkW,EAAiB7V,OACxB8V,EAAcN,EAAO9C,MACrBqD,EAAWR,EAAI7C,MAET/S,KAENmW,EADA1R,EAAIyR,EAAiBlW,IACJ8V,EAAGrR,GAErB0R,EAAYE,SAA2B,aAAhBP,EAAGO,SAA0B,WAAa,WACjD,WAAfP,EAAGQ,UAA0BH,EAAYG,QAAU,gBACpDF,EAASzD,GAAWyD,EAAS1D,GAAU,OACvCyD,EAAYI,UAAYT,EAAGS,WAAa,OACxCJ,EAAYK,SAAW,UACvBL,EAAYM,UAAY,aACxBN,EAAYO,IAAU5G,GAAS8F,EAAKpR,IAAemS,GACnDR,EAAYS,IAAW9G,GAAS8F,EAAK3V,IAAa0W,GAClDR,EAAYU,IAAYT,EAASU,IAAWV,EAAQ,IAASA,EAAQ,KAAU,IAC/EW,GAAUhB,GACVK,EAASM,IAAUN,EAAQ,SAAmBN,EAAGY,IACjDN,EAASQ,IAAWR,EAAQ,UAAoBN,EAAGc,IACnDR,EAASS,IAAYf,EAAGe,IACpBjB,EAAIoB,aAAenB,IACtBD,EAAIoB,WAAW7D,aAAa0C,EAAQD,GACpCC,EAAOzC,YAAYwC,IAEpBA,EAAII,MAAMC,WAAY,GAsBZ,SAAZgB,GAAYxa,WACPya,EAAIC,EAAY9W,OACnB0S,EAAQtW,EAAQsW,MAChBqE,EAAQ,GACRpX,EAAI,EACEA,EAAIkX,EAAGlX,IACboX,EAAMlX,KAAKiX,EAAYnX,GAAI+S,EAAMoE,EAAYnX,YAE9CoX,EAAMpY,EAAIvC,EACH2a,EAuBS,SAAjBC,GAAkBnZ,EAAO8W,EAASsC,EAAc5G,EAAW6G,EAAQC,EAAQC,EAAgBxY,EAAMyY,EAAgBC,EAAavF,EAAkBwF,EAAa/F,EAAoBgG,GAChL/I,GAAY5Q,KAAWA,EAAQA,EAAMe,IACjCuO,GAAUtP,IAAgC,QAAtBA,EAAMuP,OAAO,EAAE,KACtCvP,EAAQ0Z,GAAmC,MAApB1Z,EAAMsT,OAAO,GAAaJ,GAAY,IAAMlT,EAAMuP,OAAO,GAAI6J,GAAgB,QAGpGQ,EAAIpT,EAAIjI,EADLsb,EAAOlG,EAAqBA,EAAmBkG,OAAS,KAE5DlG,GAAsBA,EAAmBmG,KAAK,GAC9CrM,MAAMzN,KAAWA,GAASA,GACrB6Q,GAAU7Q,GAkBd2T,IAAuB3T,EAAQ7B,GAAK+C,MAAM6Y,SAASpG,EAAmBqG,cAAcC,MAAOtG,EAAmBqG,cAAcE,IAAK,EAAGR,EAAa1Z,IACjJuZ,GAAkBnE,EAAgBmE,EAAgBH,EAAc5G,GAAW,OAnBrD,CACtB5B,GAAYkG,KAAaA,EAAUA,EAAQ/V,QAE1CoZ,EAAQC,EAAaC,EAAcjC,EADhCkC,GAAWta,GAAS,KAAK8F,MAAM,KAEnCvH,EAAUsC,EAAWiW,EAAS/V,IAASgE,IACvCoV,EAAS5J,GAAWhS,IAAY,MACd4b,EAAOI,MAASJ,EAAOK,MAAgD,SAAvChJ,GAAkBjT,GAAS6Z,UAC5EA,EAAU7Z,EAAQsW,MAAMuD,QACxB7Z,EAAQsW,MAAMuD,QAAU,QACxB+B,EAAS5J,GAAWhS,GACpB6Z,EAAW7Z,EAAQsW,MAAMuD,QAAUA,EAAW7Z,EAAQsW,MAAM4F,eAAe,YAE5EL,EAAclH,GAAYoH,EAAQ,GAAIH,EAAO3H,EAAU7L,IACvD0T,EAAenH,GAAYoH,EAAQ,IAAM,IAAKlB,GAC9CpZ,EAAQma,EAAO3H,EAAUjM,GAAKiT,EAAehH,EAAUjM,GAAKkT,EAAcW,EAAcf,EAASgB,EACjGd,GAAkBnE,EAAgBmE,EAAgBc,EAAc7H,EAAY4G,EAAeiB,EAAe,IAAOd,EAAe5E,UAA2B,GAAf0F,GAC5IjB,GAAgBA,EAAeiB,KAK5BV,IACH5Y,EAAK4Y,GAAiB3Z,IAAU,KAChCA,EAAQ,IAAMA,EAAQ,IAEnBsZ,EAAQ,KACPnB,EAAWnY,EAAQoZ,EACtB/E,EAAUiF,EAAO3E,SAClBiF,EAAK,SAAWpH,EAAU5L,GAC1BwO,EAAgBkE,EAAQnB,EAAU3F,EAAY6B,GAAsB,GAAX8D,IAAoB9D,IAAYH,EAAmB3T,KAAK4C,IAAI4B,GAAM6U,GAAK/X,GAAO+X,IAAON,EAAOR,WAAWc,KAAQzB,EAAW,GAC/KjE,IACHsF,EAAiBjJ,GAAWgJ,GAC5BrF,IAAqBoF,EAAOzE,MAAMrC,EAAUzL,GAAGR,GAAMiT,EAAehH,EAAUzL,GAAGR,GAAKiM,EAAUzL,GAAG2T,EAAIpB,EAAOnE,QAAWsD,YAGvH9E,GAAsBpV,IACzBqb,EAAKrJ,GAAWhS,GAChBoV,EAAmBmG,KAAKJ,GACxBlT,EAAK+J,GAAWhS,GAChBoV,EAAmBgH,cAAgBf,EAAGpH,EAAUjM,GAAKC,EAAGgM,EAAUjM,GAClEvG,EAAQA,EAAS2T,EAAmBgH,cAAiBjB,GAEtD/F,GAAsBA,EAAmBmG,KAAKD,GACvClG,EAAqB3T,EAAQO,KAAKC,MAAMR,GAGpC,SAAZ4a,GAAarc,EAAS6V,EAAQoG,EAAKD,MAC9Bhc,EAAQua,aAAe1E,EAAQ,KAEjC7N,EAAGqR,EADA/C,EAAQtW,EAAQsW,SAEhBT,IAAWrP,GAAO,KAGhBwB,KAFLhI,EAAQsc,QAAUhG,EAAMC,QACxB8C,EAAKpG,GAAkBjT,IAEhBgI,GAAMuU,GAAWC,KAAKxU,KAAMqR,EAAGrR,IAA0B,iBAAbsO,EAAMtO,IAAyB,MAANA,IAC1EsO,EAAMtO,GAAKqR,EAAGrR,IAGhBsO,EAAM2F,IAAMA,EACZ3F,EAAM0F,KAAOA,OAEb1F,EAAMC,QAAUvW,EAAQsc,QAEzB1c,GAAKgG,KAAK6W,SAASzc,GAASqY,QAAU,EACtCxC,EAAOc,YAAY3W,IAGE,SAAvB0c,GAAwBC,EAAcC,EAAcC,OAC/CC,EAAQF,EACXG,EAAQD,SACF,SAAArb,OACFub,EAAUhb,KAAKC,MAAM0a,YACrBK,IAAYF,GAASE,IAAYD,GAAqC,EAA5B/a,KAAKyD,IAAIuX,EAAUF,IAA0C,EAA5B9a,KAAKyD,IAAIuX,EAAUD,KACjGtb,EAAQub,EACRH,GAAeA,KAEhBE,EAAQD,EACRA,EAAQrb,GAIK,SAAfwb,GAAgBlC,EAAQ9G,EAAWxS,OAC9BkH,EAAO,GACXA,EAAKsL,EAAUjM,GAAK,KAAOvG,EAC3B7B,GAAKsd,IAAInC,EAAQpS,GAUC,SAAnBwU,GAAoBC,EAAUnJ,GAGjB,SAAXoJ,GAAY/U,EAAUK,EAAMiU,EAAcU,EAASC,OAC9CC,EAAQH,GAASG,MACpBC,EAAa9U,EAAK8U,WAClBC,EAAY,GACbd,EAAeA,GAAgBe,QAC3BC,EAAuBlB,GAAqBiB,EAAWf,EAAc,WACxEY,EAAMnN,OACNgN,GAASG,MAAQ,WAElBD,EAAWD,GAAWC,GAAY,EAClCD,EAAUA,GAAYhV,EAAWsU,EACjCY,GAASA,EAAMnN,OACf1H,EAAKkV,GAAQvV,GACbK,EAAK+U,UAAYA,GACPG,GAAQ,kBAAMD,EAAqBhB,EAAeU,EAAUE,EAAMM,MAAQP,EAAUC,EAAMM,MAAQN,EAAMM,QAClHnV,EAAKoV,SAAW,WACf5c,GAAWC,QACX6V,KAEDtO,EAAK8U,WAAa,WACjBJ,GAASG,MAAQ,EACjBC,GAAcA,EAAWO,KAAKR,IAE/BA,EAAQH,GAASG,MAAQ5d,GAAKqe,GAAGb,EAAUzU,OAzBzCgV,EAAY1a,EAAema,EAAUnJ,GACxC4J,EAAO,UAAY5J,EAAUhM,UA2B9BmV,EAASS,GAAQF,GACPjJ,aAAe,kBAAM2I,GAASG,OAASH,GAASG,MAAMnN,SAAWgN,GAASG,MAAQ,IAC5Fjd,GAAa6c,EAAU,QAASO,EAAUjJ,cAC1C/O,GAAcoB,SAAWxG,GAAa6c,EAAU,YAAaO,EAAUjJ,cAChE2I,GApjBT,IAAIzd,GAAM6H,EAAc9F,GAAMyB,GAAME,GAAQkD,GAAOlG,EAAOuX,EAAcqG,GAAUC,GAAQC,GAAQC,EAAe7G,GAAarG,GAAgBmN,EAAgB9F,GAAI+F,EAAYC,EAAapM,EAAcqM,GAAOC,GAAqBjH,EAAexW,EAAa2W,EAAqBN,EAAmBD,EAAkBsH,EAAYjY,EAAUmS,EAAoBE,EAAWrH,EAC/WkN,GAiLA5H,EAwDA2B,GAEAkG,GA6DAC,GAvSApd,GAAW,EACX2C,GAAWsD,KAAKC,IAChBmX,EAAS1a,KACT0S,GAAkB,EAClBiI,GAAW,EAyBXjO,GAAY,SAAZA,UAAYtP,SAA2B,iBAAXA,GAW5Bwd,GAAOjd,KAAKyD,IAGZwQ,EAAS,QACTC,EAAU,SACV+D,GAAS,QACTE,GAAU,SACV+E,GAAS,QACTC,GAAQ,OACRC,GAAO,MACPC,GAAU,SACVjF,GAAW,UACXC,GAAU,SACViF,GAAS,QACTC,EAAU,SACVrF,GAAM,KAYNlI,GAAa,SAAbA,WAAchS,EAASwf,OAClBhC,EAAQgC,GAAoE,6BAA/CvM,GAAkBjT,GAASse,IAAkD1e,GAAKqe,GAAGje,EAAS,CAACsM,EAAG,EAAGC,EAAG,EAAGkT,SAAU,EAAGC,SAAU,EAAGC,SAAU,EAAGC,UAAW,EAAGC,UAAW,EAAGC,MAAO,EAAGC,MAAO,EAAGC,MAAO,IAAIrN,SAAS,GACtPiJ,EAAS5b,EAAQigB,+BAClBzC,GAASA,EAAM7K,SAAS,GAAGtC,OACpBuL,GAwDRsE,GAAkB,CAAC7K,WAAY,QAASC,SAAU,MAAOE,OAAQ,EAAGD,SAAU,OAAQE,WAAW,UACjG0K,GAAY,CAACC,cAAe,OAAQC,cAAe,GACnDrL,EAAY,CAACiH,IAAK,EAAGD,KAAM,EAAGsE,OAAQ,GAAKC,OAAQ,EAAGC,MAAO,GAiC7D3J,EAAkB,SAAlBA,gBAAmBkE,EAAQW,EAAOzH,EAAWwM,OACxC9X,EAAO,CAACkR,QAAS,SACpB6G,EAAOzM,EAAUwM,EAAU,MAAQ,MACnCE,EAAe1M,EAAUwM,EAAU,KAAO,OAC3C1F,EAAO6F,WAAaH,EACpB9X,EAAKsL,EAAUzO,EAAI,WAAaib,GAAW,IAAM,EACjD9X,EAAKsL,EAAUzO,GAAKib,EAAU,MAAQ,EACtC9X,EAAK,SAAW+X,EAAOpB,IAAU,EACjC3W,EAAK,SAAWgY,EAAerB,IAAU,EACzC3W,EAAKsL,EAAUjM,GAAK0T,EAAQ,KAC5B9b,GAAKsd,IAAInC,EAAQpS,IAElB8P,GAAY,GACZoI,GAAO,GAuBPC,EAAa,GACbC,EAAc,GAEd5J,EAAY,SAAZA,UAAY3W,UAASsgB,EAAWtgB,IAASsgB,EAAWtgB,GAAMwgB,IAAI,SAAA1f,UAAKA,OAASyf,GAC5E7I,EAAe,GA+Bf+I,GAAa,EAablJ,GAAc,SAAdA,YAAe3T,EAAO8c,OACjBnK,IAAoB3S,GAIxB0U,KACAH,GAAiBhT,GAAcwb,cAAe,EAC9ChgB,GAAWoT,QAAQ,SAAApB,UAAOd,GAAYc,MAAUA,EAAIhR,UAAYgR,EAAIyF,IAAMzF,WACtEiO,EAAejK,EAAU,eAC7BsH,IAAS9Y,GAAcoO,OACvBmN,GAAc5I,KACdnX,GAAWoT,QAAQ,SAAApB,GACdd,GAAYc,KACfA,EAAIrP,SAAWqP,EAAItP,OAAOyS,MAAM+K,eAAiB,QACjDlO,EAAI,MAGNsF,GAAU7H,MAAM,GAAG2D,QAAQ,SAAAhS,UAAKA,EAAE+e,YAClC7I,GAAUlE,QAAQ,SAAChS,EAAGgB,MACjBhB,EAAEgf,eAAiBhf,EAAE4W,IAAK,KACzB0E,EAAOtb,EAAEoG,KAAK6Y,WAAa,cAAgB,eAC9CC,EAAWlf,EAAE4W,IAAI0E,GAClBtb,EAAE+N,QAAO,EAAM,GACf/N,EAAEmf,iBAAiBnf,EAAE4W,IAAI0E,GAAQ4D,GACjClf,EAAE+e,aAGJ7I,GAAUlE,QAAQ,SAAAhS,OACbqC,EAAMqN,GAAW1P,EAAE6a,SAAU7a,EAAEof,OACnB,QAAfpf,EAAEoG,KAAKgT,KAAkBpZ,EAAEqf,WAAarf,EAAEoZ,IAAM/W,IAASrC,EAAEsf,aAAatf,EAAEmZ,MAAO1Z,KAAK4C,IAAIrC,EAAEmZ,MAAM,EAAG9W,IAAM,KAE7Gwc,EAAa7M,QAAQ,SAAAzB,UAAUA,GAAUA,EAAOgP,QAAUhP,EAAOgP,QAAQ,KACzE3gB,GAAWoT,QAAQ,SAAApB,GACdd,GAAYc,KACfA,EAAIrP,QAAU6I,sBAAsB,kBAAMwG,EAAItP,OAAOyS,MAAM+K,eAAiB,WAC5ElO,EAAIyF,KAAOzF,EAAIA,EAAIyF,QAGrBF,GAAmBG,EAAoB,GACvChB,EAAa9I,QACbkS,KAEAhK,EADA0B,GAAiB,GAEjBF,GAAUlE,QAAQ,SAAAhS,UAAK8P,GAAY9P,EAAEoG,KAAKoZ,YAAcxf,EAAEoG,KAAKoZ,UAAUxf,KACzEoW,GAAiBhT,GAAcwb,cAAe,EAC9ChK,EAAU,gBA3CT5W,GAAaoF,GAAe,YAAamS,KA6C3CkK,EAAc,EACdC,GAAa,EAEbhL,EAAa,SAAbA,WAAc7S,OACRuU,IAA4B,IAAVvU,EAAa,CACnCuB,GAAcuc,YAAa,EAC3BpD,IAAYA,GAAS3a,OAAO,OACxBsW,EAAIhC,GAAU7U,OACjB0X,EAAOjX,KACP8d,EAAkC,IAAjB7G,EAAOyD,EACxBjE,EAASL,GAAKhC,GAAU,GAAGqC,YAC5BmH,GAA2BnH,EAAdkH,GAAwB,EAAI,EACzCrJ,KAAmBqJ,EAAclH,GAC7BqH,IACCpL,KAAoB5F,IAA2C,IAAzBmK,EAAOvE,KAChDA,GAAkB,EAClBI,EAAU,cAEXiH,GAASW,EACTA,EAASzD,GAEN2G,GAAa,EAAG,KACnBzJ,GAAKiC,EACS,EAAPjC,MACNC,GAAUD,KAAOC,GAAUD,IAAIrU,OAAO,EAAGge,GAE1CF,GAAa,WAERzJ,GAAK,EAAGA,GAAKiC,EAAGjC,KACpBC,GAAUD,KAAOC,GAAUD,IAAIrU,OAAO,EAAGge,GAG3Cxc,GAAcuc,YAAa,EAE5BlL,EAAS,GAEVyC,EAAmB,CAhSX,OACD,MA+R0BvD,EAASD,EAAQoE,GAAUgF,GAAShF,GAAU6E,GAAQ7E,GAAU+E,GAAM/E,GAAU8E,GAAO,UAAW,aAAc,QAAS,SAAU,kBAAmB,gBAAiB,eAAgB,aAAc,WAAY,cAAe,YAAa,YAAa,SAC3RzE,EAAcjB,EAAiB2I,OAAO,CAACnI,GAAQE,GAAS,YAAa,MAAQmF,GAAQ,MAAQC,EAAS,WAAYlF,GAASD,GAAUA,GAAWgF,GAAMhF,GAAW8E,GAAQ9E,GAAWiF,GAASjF,GAAW+E,KA6CxMkD,GAAW,WACX/H,GAAY,SAAZA,UAAYK,MACPA,EAAO,KAIT3S,EAAGvG,EAHA6U,EAAQqE,EAAMpY,EAAE+T,MACnBmE,EAAIE,EAAM/W,OACVL,EAAI,OAEJoX,EAAMpY,EAAEgX,OAAS3Z,GAAKgG,KAAK6W,SAAS9B,EAAMpY,IAAI8V,QAAU,EAClD9U,EAAIkX,EAAGlX,GAAI,EACjB9B,EAAQkZ,EAAMpX,EAAE,GAChByE,EAAI2S,EAAMpX,GACN9B,EACH6U,EAAMtO,GAAKvG,EACD6U,EAAMtO,IAChBsO,EAAM4F,eAAelU,EAAEsa,QAAQD,GAAU,OAAOE,iBA4BpD3Q,GAAc,CAACoK,KAAK,EAAGC,IAAI,GA+D3BM,GAAa,qCAwFD5W,4BAQZ+C,KAAA,cAAKC,EAAM8J,WACLE,SAAWlE,KAAKiN,MAAQ,OACxB/S,MAAQ8F,KAAK4B,MAAK,GAAM,GACxB2O,QAwBJwD,EAASC,EAAUC,EAAUC,EAASC,EAASlH,EAAOC,EAAKkH,EAAaC,EAAWC,EAAoBC,EAAkBC,EAAYC,EACrIC,EAAQC,EAAkBC,EAAgBC,EAAUlK,EAAQ/W,EAAQkhB,EAAWC,EAAWC,EAAUC,EAAWC,EAAcrK,EAAasK,EAAmBC,EAC7JC,EAAiBzK,EAAI0K,EAAOC,EAAOC,GAAYC,EAAaC,EAAcC,GAAiBC,GAAYC,GAAkBC,EAAgBC,EArBrIzG,GADLpV,EAAOuK,GAAcnC,GAAUpI,IAAS2J,GAAU3J,IAASA,EAAK8b,SAAY,CAAClM,QAAS5P,GAAQA,EAAMwX,KAC/FpC,SAAU2G,EAAsO/b,EAAtO+b,YAAatY,EAAyNzD,EAAzNyD,GAAIuY,EAAqNhc,EAArNgc,SAAU5C,GAA2MpZ,EAA3MoZ,UAAW6C,EAAgMjc,EAAhMic,MAAOrM,GAAyL5P,EAAzL4P,QAASY,GAAgLxQ,EAAhLwQ,IAAK0L,GAA2Klc,EAA3Kkc,WAAYC,GAA+Jnc,EAA/Jmc,oBAAqBzE,EAA0I1X,EAA1I0X,cAAe0E,EAA2Hpc,EAA3Hoc,gBAAiBC,EAA0Grc,EAA1Gqc,eAAgBC,GAA0Ftc,EAA1Fsc,KAAMrR,GAAoFjL,EAApFiL,KAAMsR,GAA8Evc,EAA9Euc,YAAaC,EAAiExc,EAAjEwc,UAAW/P,GAAsDzM,EAAtDyM,mBAAoBgQ,GAAkCzc,EAAlCyc,cAAeC,GAAmB1c,EAAnB0c,gBACjOpR,GAAYtL,EAAK6Y,YAAe7Y,EAAKyM,qBAA0C,IAApBzM,EAAK6Y,WAAwBzZ,GAAcvE,GACtG8hB,IAAYV,GAAmB,IAAVA,EACrBxH,GAAW9a,EAAWqG,EAAKyU,UAAYzb,IACvC4jB,EAAgB3lB,GAAKgG,KAAK6W,SAASW,IACnC1O,GAAatO,GAAYgd,IACzBzH,GAA0H,WAAtG,YAAahN,EAAOA,EAAK6c,QAAUzlB,EAAcqd,GAAU,YAAe1O,IAAc,SAC5G+W,GAAY,CAAC9c,EAAK+c,QAAS/c,EAAKgd,QAAShd,EAAKid,YAAajd,EAAKkd,aAChEzF,GAAgBkF,IAAY3c,EAAKyX,cAAc7Y,MAAM,KACrDue,GAAU,YAAand,EAAOA,EAAKmd,QAAU3F,GAAU2F,QACvD5K,GAAcxM,GAAa,EAAIH,WAAW0E,GAAkBmK,IAAU,SAAWnJ,GAAUhM,GAAKqX,MAAY,EAC5G9c,GAAOiM,KACPsX,GAAgBpd,EAAKod,eAAkB,kBAAMpd,EAAKod,cAAcvjB,KAChEwjB,GAhkBa,SAAfC,aAAgB7I,EAAU1O,SAAatG,IAAAA,EAAGC,IAAAA,GAAI7C,IAAAA,SAAQA,EAAIzF,EAAcqd,EAAU,0BAA4B,kBAAM5X,IAAI4C,IAAK,kBAAOsG,EAAa8C,GAAsBnJ,GAAM+U,EAAS,SAAW/U,KAAQ,GAgkBrL4d,CAAa7I,GAAU1O,GAAYuF,IACrDiS,GAhkBgB,SAAlBC,gBAAmBnmB,EAAS0O,UAAgBA,IAAexO,GAASC,QAAQH,GAAW2R,GAAe3R,GAAW,kBAAM4R,IAgkBhGuU,CAAgB/I,GAAU1O,IAC/C0X,GAAW,EACXC,GAAc,EACdC,GAAe,EACf7R,GAAaxR,EAAema,GAAUnJ,OAMvCzR,GAAK+jB,YAAc/jB,GAAKof,WAAY,EACpCpf,GAAKmf,KAAO1N,GACZoM,GAAiB,GACjB7d,GAAK4a,SAAWA,GAChB5a,GAAKsY,OAAS1F,GAAqBA,GAAmBkG,KAAKkL,KAAKpR,IAAsBX,GACtFkO,EAAUlO,KACVjS,GAAKmG,KAAOA,EACZ8J,EAAYA,GAAa9J,EAAK8J,UAC1B,oBAAqB9J,IACxB8V,GAAQ,GACkB,OAA1B9V,EAAK8d,kBAA8B3H,GAAWtc,KAE/C+iB,EAAcmB,YAAcnB,EAAcmB,aAAe,CACxDzK,IAAKkB,GAAiBC,GAAU5Z,IAChCwY,KAAMmB,GAAiBC,GAAUrV,KAElCvF,GAAKggB,QAAUA,EAAU+C,EAAcmB,YAAYzS,GAAUjM,GAC7DxF,GAAKmkB,cAAgB,SAAAllB,IACpByiB,EAAc5R,GAAU7Q,IAAUA,GAKjCwiB,GAAaA,GAAWxQ,SAAShS,GAAUwiB,GAAarkB,GAAKqe,GAAGxL,EAAW,CAACmU,KAAM,OAAQC,cAAe,MAAOpT,SAAUyQ,EAAa4C,QAAQ,EAAMrJ,WAAY,6BAAMsH,GAAmBA,EAAgBviB,QAH1MyhB,IAAcA,GAAWtR,SAAS,GAAGtC,OACrC4T,GAAa,IAKXxR,IACHA,EAAU9J,KAAKoe,MAAO,EACrBtU,EAAUuU,WAAaxkB,GAAKykB,aAAmD,IAAnCxU,EAAU9J,KAAKue,kBAAsD,IAAzBve,EAAKue,iBAA6BzU,EAAUgB,YAAchB,EAAUqP,OAAO,GAAG,GAAM,GAC7Ktf,GAAKiQ,UAAYA,EAAU1D,SAC3B0D,EAAUgJ,cAAgBjZ,IACrBmkB,cAAc/B,GACnBb,EAAQ,EACD3X,EAAPA,GAAYqG,EAAU9J,KAAKyD,IAGxBwH,KAEErB,GAAUqB,MAASA,GAAKnQ,OAC5BmQ,GAAO,CAACuT,OAAQvT,wBAEIpN,GAAM8P,OAAU1W,GAAKsd,IAAIxO,GAAa,CAAClI,GAAOlD,IAAU8Z,GAAU,CAACiE,eAAgB,SACxGlgB,GAAWoT,QAAQ,SAAAnE,UAAKiC,GAAYjC,IAAMA,EAAEvM,UAAY6K,GAAatL,GAAKC,kBAAoBC,GAAS8Z,MAAchN,EAAEtM,QAAS,KAChI4e,EAAWrQ,GAAYuB,GAAKuT,QAAUvT,GAAKuT,OAAyB,WAAhBvT,GAAKuT,OAvjBxC,SAAnBC,iBAAmB3U,UAAa,SAAAhR,UAAS7B,GAAK+C,MAAMiR,KAAKN,GAAoBb,GAAYhR,IAujBR2lB,CAAiB3U,GAA6B,sBAAhBmB,GAAKuT,OAvhB7F,SAAvBE,qBAAuB9T,UAAY,SAAC9R,EAAO6lB,UAAO5T,GAAiBJ,GAAoBC,GAArCG,CAAgDjS,EAAO6lB,EAAGrT,YAuhByCoT,CAAqB5U,IAAkC,IAArBmB,GAAK2T,YAAwB,SAAC9lB,EAAO6lB,UAAO5T,GAAiBE,GAAKuT,OAAtBzT,CAA8BjS,EAAO4C,KAAagiB,GAAc,IAAM,EAAIiB,EAAGrT,YAAarU,GAAK+C,MAAMiR,KAAKA,GAAKuT,QAChVhD,EAAevQ,GAAKH,UAAY,CAACnP,IAAK,GAAKM,IAAK,GAChDuf,EAAe5R,GAAU4R,GAAgBhG,GAAOgG,EAAa7f,IAAK6f,EAAavf,KAAOuZ,GAAOgG,EAAcA,GAC3GC,GAAkBxkB,GAAKyP,YAAYuE,GAAK4T,OAAUtD,EAAc,GAAM,GAAK,eACtEpJ,EAASrG,KACZgT,EAAoBpjB,KAAagiB,GAAc,IAC/C7I,EAAQgF,EAAQhF,WACZiK,GAAqBzlB,KAAKyD,IAAIjD,GAAKsC,eAAiB,KAAQ0Y,GAAUrM,IAAkBiV,KAAatL,EA+B/FtY,GAAKklB,UAAYtB,KAAatL,GACxCsJ,GAAgBtW,SAAQ,OAhCyF,KAC7G6E,GAAYmI,EAASY,GAASyH,EACjC0D,EAAgBpU,IAAc6S,GAAW7S,EAAUoU,gBAAkBlU,EACrEgV,EAAWF,EAAoB,GAAMZ,EAAgB7C,IAAU3f,KAAa+Z,IAAU,KAAS,EAC/Fd,EAAU1d,GAAK+C,MAAM8D,OAAOkM,EAAU,EAAIA,EAAUsM,GAAK0I,EAAW,GAAKA,EAAW,MACpFC,EAAajV,IAA6B,IAAjBiB,GAAKiU,QAAoB,EAAIvK,GACtDwK,EAAW3J,GAAO,EAAG,EAAGuE,EAASkF,EAAYplB,KAC7CulB,EAAY/lB,KAAKC,MAAMyZ,EAAQoM,EAAW3E,GACxC6E,EAAqCpU,GAArCoU,QAASnL,EAA4BjJ,GAA5BiJ,YAAaY,EAAe7J,GAAf6J,cACrB3C,GAAUa,GAAiBD,GAAVZ,GAAmBiN,IAAcjN,EAAQ,IACzD0C,IAAUA,EAAMwJ,UAAYxJ,EAAMzX,MAAQkZ,GAAK8I,EAAYjN,WAG1C,IAAjBlH,GAAKiU,UACRvK,EAAUwK,EAAWnV,GAEtB6P,EAAQuF,EAAW,CAClBtU,SAAU0Q,EAAalF,GAAoF,KAA7Ejd,KAAK4C,IAAIqa,GAAK2I,EAAaf,GAAgB5H,GAAK6I,EAAWjB,IAA0Bc,EAAW,KAAS,IACvIf,KAAMhT,GAAKgT,MAAQ,SACnB7gB,KAAMkZ,GAAK8I,EAAYjN,GACvB+B,YAAa,8BAAMuH,GAAgBtW,SAAQ,IAAS+O,GAAeA,EAAYra,KAC/Eib,WAAY,sBACXjb,GAAK2B,SACLiiB,GAAW3R,KACXsP,EAAQC,EAAQvR,IAAc6S,GAAW7S,EAAUoU,gBAAkBrkB,GAAKmQ,SAC1EqS,GAAkBA,EAAexiB,IACjCib,GAAcA,EAAWjb,MAExBsY,EAAQwC,EAAU6F,EAAQ4E,EAAYjN,EAASwC,EAAU6F,GAC5D6E,GAAWA,EAAQxlB,GAAMggB,EAAQhF,WAKjCzO,SAEJ3C,IAAOyU,GAAKzU,GAAM5J,IAKKgiB,GADvBA,GAHAjM,GAAU/V,GAAK+V,QAAUjW,EAAWiW,KAAoB,IAARY,IAAgBA,MAGhCZ,GAAQgB,OAAShB,GAAQgB,MAAM0O,WACnBzD,EAAmBhiB,IAE/D2W,IAAc,IAARA,GAAeZ,GAAUjW,EAAW6W,IAC1CpI,GAAU2T,KAAiBA,EAAc,CAACwD,QAAS3P,GAAS4P,UAAWzD,IACnEvL,MACa,IAAf0L,IAAwBA,KAAexK,KAAawK,MAAcA,IAAc1L,GAAIoB,YAAcpB,GAAIoB,WAAWjE,OAAuD,SAA9CrD,GAAkBkG,GAAIoB,YAAYV,UAA6BO,IAC1L5X,GAAK2W,IAAMA,IACXsJ,EAAW7iB,GAAKgG,KAAK6W,SAAStD,KAChBC,OAYbgK,EAAmBX,EAASa,UAXxB6B,KACHA,EAAY7iB,EAAW6iB,MACTA,EAAUV,WAAaU,EAAYA,EAAUnI,SAAWmI,EAAUiD,eAChF3F,EAAS4F,iBAAmBlD,EAC5BA,IAAc1C,EAASnJ,YAAckB,GAAU2K,KAEhD1C,EAASrJ,OAASA,EAAS+L,GAAa/hB,GAAKsS,cAAc,OAC3D0D,EAAOkP,UAAUC,IAAI,cACrBnc,GAAMgN,EAAOkP,UAAUC,IAAI,cAAgBnc,GAC3CqW,EAASa,SAAWF,EAAmB5I,GAAUrB,MAIjC,IAAjBxQ,EAAK6f,SAAqB5oB,GAAKsd,IAAI/D,GAAK,CAACqP,SAAS,IAClDhmB,GAAK4W,OAASA,EAASqJ,EAASrJ,OAChCC,EAAKpG,GAAkBkG,IACvBwK,EAAetK,EAAGwL,GAAa5Q,GAAU9L,KACzCob,EAAY3jB,GAAKmE,YAAYoV,IAC7BqK,EAAY5jB,GAAK6oB,YAAYtP,GAAKlF,GAAUzO,EAAG0U,IAE/ChB,GAAWC,GAAKC,EAAQC,GACxBiK,EAAW9I,GAAUrB,KAElB2M,GAAS,CACZ7C,EAAa1Q,GAAUuT,IAAW5S,GAAa4S,GAAS5F,IAAmBA,GAC3E6C,EAAqB9N,GAAc,iBAAkB7I,EAAIgR,GAAUnJ,GAAWgP,EAAY,GAC1FD,EAAmB/N,GAAc,eAAgB7I,EAAIgR,GAAUnJ,GAAWgP,EAAY,EAAGF,GACzF1gB,EAAS0gB,EAAmB,SAAW9O,GAAUzL,GAAGH,QAChDqgB,EAAUpmB,EAAWvC,EAAcqd,GAAU,YAAcA,IAC/DyF,EAAcpU,KAAKoU,YAAc5N,GAAc,QAAS7I,EAAIsc,EAASzU,GAAWgP,EAAY5gB,EAAQ,EAAG+S,IACvG0N,EAAYrU,KAAKqU,UAAY7N,GAAc,MAAO7I,EAAIsc,EAASzU,GAAWgP,EAAY5gB,EAAQ,EAAG+S,IACjGA,KAAuBmP,EAAiB3kB,GAAK6oB,YAAY,CAAC5F,EAAaC,GAAY7O,GAAUzO,EAAG0U,KAC1FvE,IAAsBzV,GAAS0D,SAAsD,IAA5C7D,EAAcqd,GAAU,kBA3qBrD,SAApBuL,kBAAoB3oB,OACf4Z,EAAW3G,GAAkBjT,GAAS4Z,SAC1C5Z,EAAQsW,MAAMsD,SAAyB,aAAbA,GAAwC,UAAbA,EAAwBA,EAAW,WA0qBtF+O,CAAkBja,GAAalI,GAAQ4W,IACvCxd,GAAKsd,IAAI,CAAC6F,EAAoBC,GAAmB,CAACwF,SAAS,IAC3D5E,EAAoBhkB,GAAK6oB,YAAY1F,EAAoB9O,GAAUzO,EAAG0U,IACtE4J,EAAkBlkB,GAAK6oB,YAAYzF,EAAkB/O,GAAUzO,EAAG0U,QAIhE9E,GAAoB,KACnBwT,EAAcxT,GAAmBzM,KAAKoV,SACzC8K,EAAYzT,GAAmBzM,KAAKmgB,eACrC1T,GAAmB2T,cAAc,WAAY,WAC5CvmB,GAAK2B,OAAO,EAAG,EAAG,GAClBykB,GAAeA,EAAYI,MAAM5T,GAAoByT,GAAa,SAIpErmB,GAAKymB,SAAW,kBAAMxQ,GAAUA,GAAUtY,QAAQqC,IAAQ,IAC1DA,GAAK0mB,KAAO,kBAAMzQ,GAAUA,GAAUtY,QAAQqC,IAAQ,IAEtDA,GAAK8N,OAAS,SAACA,EAAQ6Y,OACjBA,SAAe3mB,GAAK6N,MAAK,OAC1B+Y,GAAe,IAAX9Y,IAAqB9N,GAAKqQ,QACjCwW,EAAiB7R,GACd4R,IAAM5mB,GAAKykB,aACVmC,IACH/E,GAAariB,KAAK4C,IAAI6P,KAAcjS,GAAKsY,OAAOlC,KAAO,GACvD0N,GAAe9jB,GAAKmQ,SACpB2R,GAAmB7R,GAAaA,EAAUE,YAE3CkQ,GAAe,CAACA,EAAaC,EAAWC,EAAoBC,GAAkBzO,QAAQ,SAAA4H,UAAKA,EAAE7F,MAAMuD,QAAUuP,EAAI,OAAS,UACtHA,IACH5R,GAAchV,IACT2B,OAAOilB,IAETjQ,IAAS+L,IAAgB1iB,GAAKklB,WAC7B0B,EA7bM,SAAdE,YAAenQ,EAAKC,EAAQuB,GAC3BL,GAAUK,OACNvZ,EAAQ+X,EAAII,SACZnY,EAAMinB,eACT/N,GAAUlZ,EAAMkY,kBACV,GAAIH,EAAII,MAAMC,UAAW,KAC3B3D,EAASuD,EAAOmB,WAChB1E,IACHA,EAAOa,aAAayC,EAAKC,GACzBvD,EAAOoD,YAAYG,IAGrBD,EAAII,MAAMC,WAAY,EAkblB8P,CAAYnQ,GAAKC,EAAQgK,GAEzBlK,GAAWC,GAAKC,EAAQnG,GAAkBkG,IAAMG,IAGlD8P,GAAK5mB,GAAK2B,OAAOilB,GACjB5R,GAAc6R,EACd7mB,GAAKykB,WAAamC,IAIpB5mB,GAAK8e,QAAU,SAACiI,EAAMnlB,EAAOwV,EAAU4P,OACjChS,IAAgBhV,GAAKqQ,SAAazO,KAGnC+U,IAAOoQ,GAAQxS,GAClBxW,GAAaoF,cAAe,YAAamS,UAGzCa,IAAkBoN,IAAiBA,GAAcvjB,IAClDgV,GAAchV,GACVggB,EAAQhF,QAAU5D,IACrB4I,EAAQhF,MAAMnN,OACdmS,EAAQhF,MAAQ,GAEjByG,IAAcA,GAAWlV,QACzB+V,IAAuBrS,GAAaA,EAAUnC,OAAO,CAACD,MAAM,IAAQoZ,aACpEjnB,GAAKykB,YAAczkB,GAAK8N,QAAO,GAAM,GACrC9N,GAAK+e,eAAgB,MAapBlI,EAAIuC,EAAQd,EAAQ4O,EAAYC,EAAUC,EAAYC,EAAQC,EAAgBC,EAASC,EAAcC,EAAgBC,EAAmBC,EAZrIvV,EAAOoR,KACV/K,EAAiBiL,KACjBthB,EAAMwQ,GAAqBA,GAAmB3B,WAAaxB,GAAWmL,GAAUnJ,IAChFmW,EAAiBjH,GAAU,IAC3B9gB,EAAS,EACTgoB,EAAiBb,GAAa,EAC9Bc,EAAY/X,GAAUqH,GAAYA,EAAS+B,IAAMhT,EAAKgT,IACtD4O,EAAmB5hB,EAAK6hB,YAAcjS,GACtCkS,EAAclY,GAAUqH,GAAYA,EAAS8B,MAAS/S,EAAK+S,QAAyB,IAAf/S,EAAK+S,OAAgBnD,GAAeY,GAAM,MAAQ,SAAnB,GACpGuR,EAAkBloB,GAAKkoB,gBAAkB/hB,EAAK+hB,iBAAmBpoB,EAAWqG,EAAK+hB,gBAAiBloB,IAClGmoB,EAAgBpS,IAAWvW,KAAK4C,IAAI,EAAG6T,GAAUtY,QAAQqC,MAAW,EACpEe,EAAIonB,MAED7E,IAAWvT,GAAUqH,KACxBsQ,EAAoBtqB,GAAKmE,YAAYgf,EAAoB9O,GAAUjM,GACnEmiB,EAAkBvqB,GAAKmE,YAAYif,EAAkB/O,GAAUjM,IAEzDzE,MACNqmB,EAAanR,GAAUlV,IACZoY,KAAOiO,EAAWtI,QAAQ,EAAG,KAAO9J,GAAchV,MAC7DqnB,EAASD,EAAWzQ,MACL0Q,IAAWtR,IAAWsR,IAAW1Q,IAAO0Q,IAAWa,GAAqBd,EAAW3C,cAChF+C,EAAjBA,GAAgC,IACnBY,QAAQhB,GACrBA,EAAWtZ,QAAO,GAAM,IAErBsZ,IAAenR,GAAUlV,KAC5BonB,IACApnB,SAGF8O,GAAYoY,KAAiBA,EAAcA,EAAYjoB,KACvDioB,EAAc3Z,GAAY2Z,EAAa,QAASjoB,IAChDkZ,EAAQd,GAAe6P,EAAalS,GAAS3D,EAAMX,GAAWQ,KAAcoO,EAAaE,EAAoBvgB,GAAMyY,EAAgBC,GAAavF,GAAkB/Q,EAAKwQ,GAAoB5S,GAAK+jB,aAAe,iBAAmBpN,IAAO,KAAQ,GACjP9G,GAAYiY,KAAeA,EAAYA,EAAU9nB,KAC7CuO,GAAUuZ,KAAeA,EAAUnqB,QAAQ,SACzCmqB,EAAUnqB,QAAQ,KACtBmqB,GAAavZ,GAAU0Z,GAAeA,EAAYljB,MAAM,KAAK,GAAK,IAAM+iB,GAExEjoB,EAASsS,GAAY2V,EAAUtZ,OAAO,GAAI4D,GAC1C0V,EAAYvZ,GAAU0Z,GAAeA,GAAerV,GAAqBxV,GAAK+C,MAAM6Y,SAAS,EAAGpG,GAAmB3B,WAAY2B,GAAmBqG,cAAcC,MAAOtG,GAAmBqG,cAAcE,IAAKD,GAASA,GAASrZ,EAC/NkoB,EAAmBhS,KAGrB+R,EAAYxZ,GAAYwZ,EAAW,MAAO9nB,IAC1CmZ,EAAM3Z,KAAK4C,IAAI8W,EAAOd,GAAe0P,IAAcC,EAAmB,SAAW3lB,GAAM2lB,EAAkB3V,EAAMX,GAAWQ,KAAepS,EAAQygB,EAAWE,EAAkBxgB,GAAMyY,EAAgBC,GAAavF,GAAkB/Q,EAAKwQ,GAAoB5S,GAAKof,WAAa,gBAAkB,KAEhSvf,EAAS,EACTkB,EAAIonB,EACGpnB,MAENsmB,GADAD,EAAanR,GAAUlV,IACH4V,MACNyQ,EAAWlO,MAAQkO,EAAWiB,UAAYnP,IAAUtG,IAAuC,EAAjBwU,EAAWjO,MAClGtC,EAAKuQ,EAAWjO,KAAOnZ,GAAK+jB,YAAcvkB,KAAK4C,IAAI,EAAGglB,EAAWlO,OAASkO,EAAWlO,QAC/EmO,IAAWtR,IAAWqR,EAAWlO,MAAQkO,EAAWiB,SAAWnP,GAAUmO,IAAWa,IAAoBxb,MAAMub,KACnHpoB,GAAUgX,GAAM,EAAIuQ,EAAWjX,WAEhCkX,IAAW1Q,KAAQkR,GAAkBhR,OAGvCqC,GAASrZ,EACTsZ,GAAOtZ,EACPG,GAAK+jB,cAAgB/jB,GAAK+jB,aAAelkB,GAErCG,GAAKof,YAAcjJ,KACtBnW,GAAKof,UAAYjG,IAAQ,KACzBA,EAAM3Z,KAAKsC,IAAIqX,EAAK1J,GAAWmL,GAAUnJ,MAE1CkP,EAAUxH,EAAMD,IAAYA,GAAS,MAAS,KAE1C0O,IACH9D,GAAe1mB,GAAK+C,MAAM8D,MAAM,EAAG,EAAG7G,GAAK+C,MAAMmoB,UAAUpP,EAAOC,EAAK0I,MAExE7hB,GAAKqoB,SAAWR,EACZxH,GAAexgB,KAClBgX,EAAK,IACFpF,GAAUzO,GAAK,KAAOnD,EACzBqoB,IAAoBrR,EAAGpF,GAAUjM,GAAK,KAAOyM,MAC7C7U,GAAKsd,IAAI,CAAC2F,EAAaC,GAAYzJ,IAGhCF,GACHE,EAAKpG,GAAkBkG,IACvBuQ,EAAazV,KAAczQ,GAC3BsX,EAASrG,KACTgP,EAAWlV,WAAWgV,EAAUtP,GAAUzO,IAAM6kB,GAC3CzlB,GAAa,EAAN+W,IAEXsO,EAAiB,CAAC3T,MADlB2T,GAAkBvb,GAActL,GAAKC,kBAAoBC,GAAU8Z,IAAU9G,MACpC7U,MAAOwoB,EAAe,WAAahW,GAAUzO,EAAEulB,gBACpFrc,IAAmF,WAArEuE,GAAkBzM,IAAO,WAAayN,GAAUzO,EAAEulB,iBACnEd,EAAe3T,MAAM,WAAarC,GAAUzO,EAAEulB,eAAiB,WAGjE7R,GAAWC,GAAKC,EAAQC,GACxBiK,EAAW9I,GAAUrB,IAErByC,EAAS5J,GAAWmH,IAAK,GACzB2Q,EAAiBnU,IAAoB1S,EAAema,GAAUsM,EAAa3hB,GAAcvE,GAApDP,GACjC4hB,MACHvL,EAAc,CAACuL,GAAa5Q,GAAU9L,IAAKgb,EAASkH,EAAiBnQ,KACzD3X,EAAI6W,GAChB7V,EAAKshB,KAAezK,GAAY/G,GAAS8F,GAAKlF,IAAakP,EAASkH,EAAiB,IAChF/Q,EAAY7V,KAAKwQ,GAAU7L,EAAG7E,EAAI2W,IACvCI,GAAUhB,GACNoR,GACHjS,GAAUlE,QAAQ,SAAAhS,GACbA,EAAE4W,MAAQuR,IAAyC,IAAtBnoB,EAAEoG,KAAKkc,aACvCtiB,EAAEgf,eAAgB,KAIrB5L,IAAoBlB,GAAW4P,KAE5B1O,MACHgU,EAAW,CACV1N,IAAML,EAAOK,KAAOyN,EAAa5O,EAASY,EAAQoO,GAAmB5P,GACrE8B,KAAOJ,EAAOI,MAAQ0N,EAAaI,EAAiBhP,EAASY,GAAUxB,GACvEF,UAAW,aACXJ,SAAU,UAEFK,IAAU0P,EAAQ,SAAmB3nB,KAAKgpB,KAAKpP,EAAO/J,OAASqI,GACxEyP,EAASxP,IAAWwP,EAAQ,UAAoB3nB,KAAKgpB,KAAKpP,EAAO7J,QAAUmI,GAC3EyP,EAAStP,IAAWsP,EAAStP,GAAU+E,IAAQuK,EAAStP,GAAU6E,IAAUyK,EAAStP,GAAUgF,IAAWsK,EAAStP,GAAU8E,IAAS,IACtIwK,EAASvP,IAAYf,EAAGe,IACxBuP,EAASvP,GAAWgF,IAAQ/F,EAAGe,GAAWgF,IAC1CuK,EAASvP,GAAW8E,IAAU7F,EAAGe,GAAW8E,IAC5CyK,EAASvP,GAAWiF,IAAWhG,EAAGe,GAAWiF,IAC7CsK,EAASvP,GAAW+E,IAAS9F,EAAGe,GAAW+E,IAC3CkE,EAjhBS,SAAb4H,WAActQ,EAAOgP,EAAUuB,WAI7BljB,EAHG8K,EAAS,GACZ2H,EAAIE,EAAM/W,OACVL,EAAI2nB,EAAc,EAAI,EAEhB3nB,EAAIkX,EAAGlX,GAAK,EAClByE,EAAI2S,EAAMpX,GACVuP,EAAOrP,KAAKuE,EAAIA,KAAK2hB,EAAYA,EAAS3hB,GAAK2S,EAAMpX,EAAE,WAExDuP,EAAOvQ,EAAIoY,EAAMpY,EACVuQ,EAugBamY,CAAW7H,EAAkBuG,EAAUzE,IACxDvM,IAAkBlE,GAAW,IAE1BhC,GACHsX,EAAUtX,EAAUuU,SACpBtI,GAAoB,GACpBjM,EAAUqP,OAAOrP,EAAUgB,YAAY,GAAM,GAC7CiQ,EAAYH,EAAUtP,GAAUzO,GAAKie,EAAWN,EAASkH,EACzDxG,EAA0C,EAA/B7hB,KAAKyD,IAAI0d,EAASO,GAC7B/N,IAAoBkO,GAAYR,EAAe9S,OAAO8S,EAAezf,OAAS,EAAG,GACjF6O,EAAUqP,OAAO,GAAG,GAAM,GAC1BiI,GAAWtX,EAAUgX,YAAW,GAChChX,EAAUoD,QAAUpD,EAAUM,UAAUN,EAAUM,aAClD2L,GAAoB,IAEpBgF,EAAYP,EAEb8G,IAAmBA,EAAexoB,MAASwoB,EAAe3T,MAAM,WAAarC,GAAUzO,EAAEulB,eAAiBd,EAAexoB,MAASwoB,EAAe3T,MAAM4F,eAAe,YAAcjI,GAAUzO,SACxL,GAAI+S,IAAW9D,OAAiBW,OACtCwG,EAASrD,GAAQgC,WACVqB,GAAUA,IAAWpV,IACvBoV,EAAOuP,aACVzP,GAASE,EAAOuP,WAChBxP,GAAOC,EAAOuP,YAEfvP,EAASA,EAAOrB,WAGlByP,GAAgBA,EAAazV,QAAQ,SAAAhS,UAAKA,EAAE+N,QAAO,GAAO,KAC1D9N,GAAKkZ,MAAQA,EACblZ,GAAKmZ,IAAMA,EACXgH,EAAUC,EAAUjK,GAAiB0L,GAAa5P,KAC7CW,IAAuBuD,KAC3BgK,EAAU0B,IAAc5P,GAAW4P,IACnC7hB,GAAKsY,OAAOlC,IAAM,GAEnBpW,GAAK8N,QAAO,GAAO,GACnB+V,GAAchiB,KACV+f,KACHgC,IAAY,EAEZhC,GAAgBtW,SAAQ,IAEzB0J,GAAc,EACd/E,GAAa6S,KAAa7S,EAAUuU,UAAY1C,KAAqB7R,EAAUE,aAAe2R,IAAoB7R,EAAUE,SAAS2R,IAAoB,GAAG,GAAMxC,OAAOrP,EAAU6I,QAAQ,GAAM,IAC7L8O,GAAkB9D,KAAiB9jB,GAAKmQ,UAAYyC,MACvD3C,IAAc6S,IAAY7S,EAAUoU,cAAczR,IAAsBsG,GAAS,OAAU4K,GAAe1mB,GAAK+C,MAAMmoB,UAAUpP,EAAOC,EAAK,GAAK2K,IAAc,GAC9J9jB,GAAKmQ,SAAWyX,IAAoBzH,EAAUjH,GAASyH,IAAWmD,GAAgB,EAAIA,IAEvFnN,IAAO0L,KAAezL,EAAO+R,WAAanpB,KAAKC,MAAMO,GAAKmQ,SAAW+Q,IACrEO,IAAcA,GAAWwF,aAEpBva,MAAMgb,KACVA,GAAqBtqB,GAAKmE,YAAYgf,EAAoB9O,GAAUjM,GACpEmiB,GAAmBvqB,GAAKmE,YAAYif,EAAkB/O,GAAUjM,GAChEiV,GAAa8F,EAAoB9O,GAAWiW,GAC5CjN,GAAa4F,EAAa5O,GAAWiW,GAAqBV,GAAa,IACvEvM,GAAa+F,EAAkB/O,GAAWkW,GAC1ClN,GAAa6F,EAAW7O,GAAWkW,GAAmBX,GAAa,KAGpEY,IAAmBzR,IAAkBnW,GAAK2B,UAEtC4d,IAAcpJ,IAAmBuK,IACpCA,GAAqB,EACrBnB,GAAUvf,IACV0gB,GAAqB,KAIvB1gB,GAAKsC,YAAc,kBAAQ2P,KAAemO,IAAYve,KAAa+Z,IAAU,KAAS,GAEtF5b,GAAK4oB,aAAe,WACnB5Y,GAAchQ,GAAKwQ,mBACfP,IACHwR,GAAaA,GAAWtR,SAAS,GAAOF,EAAUqU,SAA4DxB,IAAY9S,GAAcC,EAAWjQ,GAAKyR,UAAY,EAAG,GAA1GzB,GAAcC,EAAWA,EAAUC,cAIlGlQ,GAAK6oB,cAAgB,SAAAC,UAAS7Y,GAAaA,EAAUe,SAAYkI,GAASlZ,GAAK8e,WAAa5F,GAAUjJ,EAAUe,OAAO8X,GAAS7Y,EAAUgB,WAAc0P,GAAW,GAEnK3gB,GAAK+oB,YAAc,SAAAplB,OACd5C,EAAIkV,GAAUtY,QAAQqC,IACzBgD,EAAqB,EAAjBhD,GAAKyR,UAAgBwE,GAAU7H,MAAM,EAAGrN,GAAGioB,UAAY/S,GAAU7H,MAAMrN,EAAE,UACtEwN,GAAU5K,GAAQX,EAAE2K,OAAO,SAAA5N,UAAKA,EAAEoG,KAAK0c,kBAAoBlf,IAAQX,GAAG2K,OAAO,SAAA5N,UAAsB,EAAjBC,GAAKyR,UAAgB1R,EAAEoZ,KAAOD,EAAQnZ,EAAEmZ,OAASC,KAI5InZ,GAAK2B,OAAS,SAACU,EAAOsd,EAAgBsJ,OACjCrW,IAAuBqW,GAAc5mB,OAOxC6iB,EAAqBgE,EAAaC,EAAQC,EAAcC,EAASC,EAASC,EAJvEjR,GAA4B,IAAnBnC,GAA0B0L,GAAa7hB,GAAKsY,SACxD9S,EAAInD,EAAQ,GAAKiW,EAASY,GAASyH,EACnC6I,EAAUhkB,EAAI,EAAI,EAAQ,EAAJA,EAAQ,EAAIA,GAAK,EACvCse,EAAe9jB,GAAKmQ,YAEjBwP,IACHS,EAAUD,EACVA,EAAUvN,GAAqBX,KAAeqG,EAC1ClH,KACHoQ,EAAQD,EACRA,EAAQtR,IAAc6S,GAAW7S,EAAUoU,gBAAkBmF,IAI9D3L,IAAkB2L,GAAW7S,KAAQ3B,KAAgB9V,IAAYqV,IAAmB2E,EAAQZ,GAAWA,EAAS8H,IAAYve,KAAa+Z,IAAWiC,IAAmB2L,EAAU,MAC9KA,IAAY1F,GAAgB9jB,GAAKqQ,QAAS,IAI7C+Y,GADAC,GAFAnE,EAAWllB,GAAKklB,WAAasE,GAAWA,EAAU,OACpC1F,GAAgBA,EAAe,OAEjB0F,KAAc1F,EAC1C9jB,GAAKyR,UAAsBqS,EAAV0F,EAAyB,GAAK,EAC/CxpB,GAAKmQ,SAAWqZ,EAEZJ,IAAiBpU,KACpBkU,EAAcM,IAAY1F,EAAe,EAAgB,IAAZ0F,EAAgB,EAAqB,IAAjB1F,EAAqB,EAAI,EACtFhB,KACHqG,GAAWE,GAA8C,SAAnCzL,GAAcsL,EAAc,IAAiBtL,GAAcsL,EAAc,IAAOtL,GAAcsL,GACpHK,EAAiBtZ,IAAyB,aAAXkZ,GAAoC,UAAXA,GAAsBA,KAAUlZ,KAI1F4S,KAAoBwG,GAAWE,KAAoBA,GAAkBnH,IAAUnS,KAAeJ,GAAYgT,IAAmBA,GAAgB7iB,IAAQA,GAAK+oB,YAAYlG,IAAiB9Q,QAAQ,SAAAhS,UAAKA,EAAE6oB,kBAEjM9F,MACArB,IAAezM,IAAgB9V,GAQxB+Q,GACVA,EAAUoU,cAAcmF,KAAYxU,KAAgB6O,KAAexhB,KARlEof,GAAWgI,IAAIC,MAAQjI,GAAWkI,SAAWlI,GAAWiI,OAAUjI,GAAWnC,OAAOmC,GAAWgI,IAAIC,MAAQjI,GAAWkI,QACnHlI,GAAWmI,QACdnI,GAAWmI,QAAQ,gBAAiBJ,EAASvZ,EAAU4Z,OAAS5Z,EAAU6Z,QAE1ErI,GAAWtb,KAAKke,cAAgBmF,EAChC/H,GAAWwF,aAAa3b,aAMvBqL,MACHtU,GAASggB,KAAezL,EAAO9C,MAAMuO,GAAa5Q,GAAU9L,KAAOwb,GAC9DhO,IAEE,GAAIiW,EAAc,IACxBE,GAAWjnB,GAAmByhB,EAAV0F,GAAoClR,EAAVa,EAAM,GAAcb,EAAS,GAAK7I,GAAWmL,GAAUnJ,IACjGiR,MACErgB,IAAU6iB,IAAYoE,EAK1BzP,GAAUlD,GAAKC,OALqB,KAChCwC,EAAS5J,GAAWmH,IAAK,GAC5B9W,EAASyY,EAASY,EACnBW,GAAUlD,GAAK3S,GAAQoV,EAAOK,KAAOhI,KAAczQ,GAAYnB,EAAS,GAAM6X,GAAM0B,EAAOI,MAAQ/H,KAAczQ,GAAY,EAAInB,GAAW6X,IAK9II,GAAUoN,GAAYoE,EAAUzI,EAAiBC,GAChDO,GAAYmI,EAAU,GAAKtE,GAAalE,EAAUC,GAAwB,IAAZuI,GAAkBF,EAAsB,EAAZpI,UAb3FF,EAAUlS,GAAOmS,EAAWC,EAAYsI,KAgB1CpY,IAAS4O,EAAQhF,OAAUhG,IAAgB9V,IAAY0iB,GAAgBtW,SAAQ,GAC/E4W,IAAgBmH,GAAY5G,IAAQ+G,IAAYA,EAAU,IAAMpN,MAAsBV,GAASwG,EAAYwD,SAAS3T,QAAQ,SAAAlU,UAAMA,EAAGioB,UAAUZ,GAAYzC,GAAO,MAAQ,UAAUP,EAAYyD,cAChMpK,GAAauH,IAAazgB,GAASkZ,EAASvb,IACxCopB,IAAiBpU,IAChB8N,KACCyG,IACY,aAAXJ,EACHlZ,EAAU1D,QAAQ8X,cAAc,GACX,UAAX8E,EACVlZ,EAAU3E,SAAQ,GAAMiB,QACH,YAAX4c,EACVlZ,EAAU3E,SAAQ,GAElB2E,EAAUkZ,MAGZ5N,GAAYA,EAASvb,MAElBqpB,GAAYjN,KACf+F,GAAYkH,GAAWjZ,GAAUpQ,GAAMmiB,GACvCc,GAAUiG,IAAgB9Y,GAAUpQ,GAAMijB,GAAUiG,IACpDzG,KAAqB,IAAZ+G,EAAgBxpB,GAAK6N,MAAK,EAAO,GAAMoV,GAAUiG,GAAe,GACpEG,GAEJpG,GADAiG,EAA0B,IAAZM,EAAgB,EAAI,IACRpZ,GAAUpQ,GAAMijB,GAAUiG,KAGlDtG,KAAkBsC,GAAY1lB,KAAKyD,IAAIjD,GAAKsC,gBAAkBwN,GAAU8S,IAAiBA,GAAgB,QAC5G5S,GAAchQ,GAAKwQ,mBACnBiR,GAAaA,GAAWtR,SAAS,GAAKH,GAAcC,EAAsB,YAAXkZ,EAAuB,GAAKK,EAAS,KAE3F1G,IAAYvH,IAAavG,IACnCuG,EAASvb,OAIPshB,EAAiB,KAChByI,EAAInX,GAAqB0F,EAAS1F,GAAmB3B,YAAc2B,GAAmBgH,eAAiB,GAAKtB,EAChH8I,EAAkB2I,GAAKxJ,EAAmBnC,WAAa,EAAI,IAC3DkD,EAAgByI,GAEjBhI,GAAkBA,GAAgBzJ,EAAS1F,GAAmB3B,YAAc2B,GAAmBgH,eAAiB,MAGjH5Z,GAAKwN,OAAS,SAACnL,EAAOyc,GAChB9e,GAAKqQ,UACTrQ,GAAKqQ,SAAU,EACftS,GAAa6c,GAAU,SAAU7F,IACjC7I,IAAcnO,GAAa6c,GAAU,SAAUpc,IAC/C+kB,IAAiBxlB,GAAaoF,cAAe,cAAeogB,KAC9C,IAAVlhB,IACHrC,GAAKmQ,SAAW2T,GAAe,EAC/B3D,EAAUC,EAAUwD,GAAW3R,OAEpB,IAAZ6M,GAAqB9e,GAAK8e,YAI5B9e,GAAK6a,SAAW,SAAAzJ,UAAQA,GAAQ4O,EAAUA,EAAQhF,MAAQyG,IAE1DzhB,GAAKqf,aAAe,SAAC2K,EAAUC,EAAQC,EAAWlD,MAC7CpU,GAAoB,KACnBkS,EAAKlS,GAAmBqG,cAC3BhI,EAAW2B,GAAmB3B,WAC9B0P,EAASmE,EAAG3L,IAAM2L,EAAG5L,MACtB8Q,EAAWlF,EAAG5L,MAAQyH,EAASqJ,EAAW/Y,EAC1CgZ,EAASnF,EAAG5L,MAAQyH,EAASsJ,EAAShZ,EAEvCjR,GAAK8e,SAAQ,GAAO,EAAO,CAAC5F,MAAOzK,GAAWub,EAAUE,KAAelqB,GAAK+jB,aAAc5K,IAAK1K,GAAWwb,EAAQC,KAAelqB,GAAKof,YAAa4H,GACnJhnB,GAAK2B,UAGN3B,GAAKkf,iBAAmB,SAAAiL,MACnBrT,GAAeqT,EAAQ,KACtBppB,EAAI+V,EAAYnZ,QAAQ8T,GAAU7L,GAAK,EAC3CkR,EAAY/V,GAAMgL,WAAW+K,EAAY/V,IAAMopB,EAAUzS,GACzDZ,EAAY,GAAM/K,WAAW+K,EAAY,IAAMqT,EAAUzS,GACzDI,GAAUhB,KAIZ9W,GAAK0N,QAAU,SAACrL,EAAO+nB,MAClBpqB,GAAKqQ,WACE,IAAVhO,GAAmBrC,GAAK8N,QAAO,GAAM,GACrC9N,GAAKqQ,QAAUrQ,GAAKklB,UAAW,EAC/BkF,GAAmB3I,IAAcA,GAAWlV,QAC5CsV,GAAa,EACb5B,IAAaA,EAASpK,QAAU,GAChC0N,IAAiBjlB,GAAgB6E,cAAe,cAAeogB,IAC3D3B,KACHA,GAAgBrV,QAChByT,EAAQhF,OAASgF,EAAQhF,MAAMnN,SAAWmS,EAAQhF,MAAQ,KAEtD9O,IAAY,SACZnL,EAAIkV,GAAU7U,OACXL,QACFkV,GAAUlV,GAAG6Z,WAAaA,IAAY3E,GAAUlV,KAAOf,UAI5D1B,GAAgBsc,GAAU,SAAU7F,IACpC7I,IAAc5N,GAAgBsc,GAAU,SAAUpc,MAKrDwB,GAAK6N,KAAO,SAACC,EAAQsc,GACpBpqB,GAAK0N,QAAQI,EAAQsc,GACrB3I,KAAe2I,GAAkB3I,GAAW5T,OAC5CjE,UAAcyU,GAAKzU,OACf7I,EAAIkV,GAAUtY,QAAQqC,IACrB,GAALe,GAAUkV,GAAUlI,OAAOhN,EAAG,GAC9BA,IAAMiV,IAAmB,EAAbyJ,IAAkBzJ,KAG9BjV,EAAI,EACJkV,GAAUlE,QAAQ,SAAAhS,UAAKA,EAAE6a,WAAa5a,GAAK4a,WAAa7Z,EAAI,KAC5DA,GAAKoV,KAAmBnW,GAAKsY,OAAOlC,IAAM,GAEtCnG,IACHA,EAAUgJ,cAAgB,KAC1BnL,GAAUmC,EAAUnC,OAAO,CAACD,MAAM,IAClCuc,GAAkBna,EAAUpC,QAE7BwS,GAAe,CAACA,EAAaC,EAAWC,EAAoBC,GAAkBzO,QAAQ,SAAA4H,UAAKA,EAAE5B,YAAc4B,EAAE5B,WAAWtB,YAAYkD,KACpI2C,KAAatc,KAASsc,GAAW,GAC7B3F,KACHsJ,IAAaA,EAASpK,QAAU,GAChC9U,EAAI,EACJkV,GAAUlE,QAAQ,SAAAhS,UAAKA,EAAE4W,MAAQA,IAAO5V,MACxCA,IAAMkf,EAASrJ,OAAS,IAEzBzQ,EAAKkkB,QAAUlkB,EAAKkkB,OAAOrqB,KAG5BiW,GAAUhV,KAAKjB,IACfA,GAAKwN,QAAO,GAAO,GACnBwU,GAAsBA,EAAmBhiB,IAErCiQ,GAAaA,EAAU8V,MAAQpF,EAAQ,KACtC2J,EAAatqB,GAAK2B,OACtB3B,GAAK2B,OAAS,WACb3B,GAAK2B,OAAS2oB,EACdpR,GAASC,GAAOnZ,GAAK8e,WAEtB1hB,GAAKyP,YAAY,IAAM7M,GAAK2B,QAC5Bgf,EAAS,IACTzH,EAAQC,EAAM,OAEdnZ,GAAK8e,UAENnI,IA/+BkB,SAAnB4T,sBACKlO,KAAoBoC,GAAY,KAC/B7U,EAAKyS,GAAkBoC,GAC3BtU,sBAAsB,kBAAMP,IAAO6U,IAAclJ,IAAY,MA4+BvDgV,aAtpBD5oB,OAASsK,KAAK6S,QAAU7S,KAAK4B,KAAOgB,kBA0pBpCX,SAAP,kBAAgB9K,UACV6B,IACJ7H,GAAOgG,GAAQjG,KACf4R,MAAmB1R,OAAOwG,UAAYV,cAAcqK,SACpDvI,EAAeuX,IAETvX,iBAGD2L,SAAP,kBAAgBvQ,MACXA,MACE,IAAImF,KAAKnF,EACbsd,GAAUnY,GAAKnF,EAAOmF,UAGjBmY,kBAGDjQ,QAAP,iBAAerL,EAAOwL,GACrB2O,GAAW,EACXvG,GAAUlE,QAAQ,SAAAgE,UAAWA,EAAQlI,EAAO,OAAS,WAAWxL,KAChE/D,GAAgBa,GAAM,QAASX,IAC/BF,GAAgBsC,GAAM,SAAUpC,IAChCgsB,cAAc3O,GACdvd,GAAgBsC,GAAM,cAAeiO,IACrCvQ,GAAgB0F,GAAO,aAAc6K,IACrC+C,GAAetT,GAAiBsC,GAAM,mCAAoC8N,IAC1EkD,GAAetT,GAAiBsC,GAAM,6BAA8BgO,IACpEyG,EAAaxH,OACb6B,GAAoBpR,QACf,IAAIyC,EAAI,EAAGA,EAAIpC,GAAWyC,OAAQL,GAAG,EACzCiR,GAAe1T,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,IAC5DiR,GAAe1T,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,mBAIvDyM,OAAP,qBACCrO,GAAO9B,OACPuD,GAAOiD,SACP/C,GAASF,GAAKmD,gBACdC,GAAQpD,GAAKkD,KACT1G,KACHse,GAAWte,GAAK+C,MAAMC,QACtBub,GAASve,GAAK+C,MAAM8D,MACpBC,EAAW9G,GAAKgG,KAAKe,SAAW0K,GAChCqN,GAAsB9e,GAAKgG,KAAKqnB,oBAAsB5b,GACtDwH,EAAqBlX,GAAKC,QAAQC,mBAAqB,OACvDmgB,EAAcrgB,GAAK8G,YACnB7I,GAAKgG,KAAKC,QAAQ,gBAAiBF,eAC/Ba,IAAO,CACVwY,GAAW,GACXjG,EAAY1S,SAASqP,cAAc,QACzBY,MAAMvE,OAAS,QACzBgH,EAAUzC,MAAMsD,SAAW,WAC3Bd,KAzwCU,SAAboU,oBAAmBlO,IAAYrS,sBAAsBugB,YA0wClDA,GACApmB,EAAS4J,SAAS9Q,IAElB+F,cAAcoB,QAAUD,EAASC,QACjC4X,EAAa7X,EAASC,SAAW,0BAA0ByV,KAAKtV,UAAUimB,WAC1E5sB,GAAaoB,GAAM,QAASX,IAC5BV,EAAQ,CAACqB,GAAMyB,GAAME,GAAQkD,IACzB5G,GAAKoH,YACRrB,cAAcqB,WAAa,SAAA2B,OAEzBX,EADGolB,EAAKxtB,GAAKoH,iBAETgB,KAAKW,EACTykB,EAAG7E,IAAIvgB,EAAGW,EAAKX,WAETolB,GAERxtB,GAAKgB,iBAAiB,iBAAkB,kBAAM0X,OAC9C1Y,GAAKgB,iBAAiB,mBAAoB,kBAAMoX,OAChDpY,GAAKgB,iBAAiB,aAAc,WACnCmX,GAAY,EAAG,GACfZ,EAAU,gBAEXvX,GAAKoH,WAAW,0BAA2B,kBAC1CoQ,KACOA,MAGRrU,QAAQC,KAAK,iCAEdoU,KACA7W,GAAa6C,GAAM,SAAUpC,QAI5B4a,EAAQrY,EAHL8pB,EAAY7mB,GAAM8P,MACrBgX,EAASD,EAAUE,eACnBC,EAAiB5tB,GAAKgG,KAAK6nB,UAAUC,cAEtCF,EAAeld,QAAUqd,OAAOC,eAAeJ,EAAgB,SAAU,CAAE/rB,MAAO,wBAAoBgN,KAAK6M,MAAM,KAAM,MACvH+R,EAAUE,eAAiB,QAC3B3R,EAAS5J,GAAWxL,IACpBhD,GAAU2Y,EAAIna,KAAKC,MAAM2Z,EAAOK,IAAMzY,GAAUL,OAAS,EACzD4E,GAAYoU,EAAIna,KAAKC,MAAM2Z,EAAOI,KAAOjU,GAAY5E,OAAS,EAC9DmqB,EAAUD,EAAUE,eAAiBD,EAAUD,EAAUnR,eAAe,oBAExEmC,EAAgBwP,YAAY/W,GAAO,KACnClX,GAAKyP,YAAY,GAAK,kBAAM3N,GAAW,IACvCnB,GAAa6C,GAAM,cAAeiO,IAClC9Q,GAAaiG,GAAO,aAAc6K,IAClC+C,GAAe7T,GAAc6C,GAAM,mCAAoC8N,IACvEkD,GAAe7T,GAAc6C,GAAM,6BAA8BgO,IACjEkN,EAAiB1e,GAAK+C,MAAMmrB,YAAY,aACxCpT,EAAYjX,KAAK6a,GACjB7W,EAAepD,KACfwT,EAAejY,GAAKyP,YAAY,GAAK0I,IAAahJ,QAClDqD,EAAe,CAAChP,GAAM,mBAAoB,eACrC2qB,EAAIpsB,GAAKmQ,WACZkc,EAAIrsB,GAAKuM,YACN9K,GAAK6qB,QACR1P,EAAawP,EACbvP,EAAcwP,GACJzP,IAAewP,GAAKvP,IAAgBwP,GAC9CzW,MAECnU,GAAM,mBAAoB2U,GAAapW,GAAM,OAAQoW,GAAapW,GAAM,SAAU4V,IACrFrF,GAAoB3R,IACpBkY,GAAUlE,QAAQ,SAAAgE,UAAWA,EAAQvI,OAAO,EAAG,KAC1CzM,EAAI,EAAGA,EAAIpC,GAAWyC,OAAQL,GAAG,EACrCiR,GAAe1T,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,IAC5DiR,GAAe1T,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,oBAMzDV,OAAP,gBAAc8F,sBACQA,IAAUiW,KAAoBjW,EAAKulB,oBACpDC,EAAKxlB,EAAKylB,aACdD,GAAMnB,cAAc3O,KAAoBA,EAAgB8P,IAAON,YAAY/W,GAAOqX,0BACzDxlB,IAAUiP,EAAgD,IAA1BjS,cAAcoB,SAAiB4B,EAAK0lB,oBACzF,sBAAuB1lB,IAC1BuJ,GAAoBpR,KAAoBoR,GAAoB3R,GAAcoI,EAAK2lB,mBAAqB,QACpG7W,GAAqE,KAApD9O,EAAK2lB,kBAAoB,IAAInuB,QAAQ,0BAIjDouB,cAAP,uBAAqB1qB,EAAQ8E,OACxBpG,EAAID,EAAWuB,GAClBN,EAAIpC,GAAWhB,QAAQoC,GACvBmM,EAAatO,GAAYmC,IACrBgB,GACJpC,GAAWoP,OAAOhN,EAAGmL,EAAa,EAAI,GAEnC/F,IACH+F,EAAaxO,GAAS0qB,QAAQjpB,GAAMgH,EAAMnC,GAAOmC,EAAMrF,GAAQqF,GAAQzI,GAAS0qB,QAAQroB,EAAGoG,mBAItF6lB,gBAAP,yBAAuBrW,GACtBM,GAAUlE,QAAQ,SAAAhS,UAAKA,EAAEE,MAAQF,EAAEE,KAAK0V,QAAUA,GAAS5V,EAAEE,KAAK4N,MAAK,GAAM,oBAGvEoe,aAAP,sBAAoBzuB,EAAS8d,EAAO0D,OAC/B5F,GAAU7K,GAAU/Q,GAAWsC,EAAWtC,GAAWA,GAASigB,wBACjE5d,EAASuZ,EAAO4F,EAAavH,GAASE,IAAW2D,GAAS,SACpD0D,EAAqC,EAAxB5F,EAAO4E,MAAQne,GAAcuZ,EAAOI,KAAO3Z,EAASV,GAAKmQ,WAAsC,EAAzB8J,EAAO2E,OAASle,GAAcuZ,EAAOK,IAAM5Z,EAASV,GAAKuM,2BAG7IwgB,mBAAP,4BAA0B1uB,EAAS2uB,EAAgBnN,GAClDzQ,GAAU/Q,KAAaA,EAAUsC,EAAWtC,QACxC4b,EAAS5b,EAAQigB,wBACpBrL,EAAOgH,EAAO4F,EAAavH,GAASE,IACpC9X,EAA2B,MAAlBssB,EAAyB/Z,EAAO,EAAM+Z,KAAkB3Z,EAAaA,EAAU2Z,GAAkB/Z,GAAQ+Z,EAAexuB,QAAQ,KAAOoO,WAAWogB,GAAkB/Z,EAAO,IAAMrG,WAAWogB,IAAmB,SAClNnN,GAAc5F,EAAOI,KAAO3Z,GAAUV,GAAKmQ,YAAc8J,EAAOK,IAAM5Z,GAAUV,GAAKuM,2BAGtF0gB,QAAP,iBAAeC,MACdpW,GAAU7H,MAAM,GAAG2D,QAAQ,SAAAhS,SAAmB,mBAAdA,EAAEoG,KAAKyD,IAA2B7J,EAAE8N,UAC7C,IAAnBwe,EAAyB,KACxBC,EAAYhO,EAAW8N,SAAW,GACtC9N,EAAa,GACbgO,EAAUva,QAAQ,SAAAjT,UAAKA,8CAj1BbqH,EAAM8J,GACjBhL,GAAgB9B,cAAc+K,SAAS9Q,KAASmD,QAAQC,KAAK,6CAC7D0D,EAAS+H,WACJ/F,KAAKC,EAAM8J,MAo1BJjC,QAAU,YACVue,WAAa,SAAA7G,UAAWA,EAAUhK,GAASgK,GAAS3T,QAAQ,SAAA1Q,MACrEA,GAAUA,EAAOyS,MAAO,KACvB/S,EAAI2U,EAAa/X,QAAQ0D,GACxB,GAALN,GAAU2U,EAAa3H,OAAOhN,EAAG,GACjC2U,EAAazU,KAAKI,EAAQA,EAAOyS,MAAMC,QAAS1S,EAAOuU,SAAWvU,EAAOmrB,aAAa,aAAcpvB,GAAKgG,KAAK6W,SAAS5Y,GAAS6C,QAE7HwR,MACS5H,OAAS,SAACiZ,EAAMtR,UAAUK,IAAYiR,EAAMtR,OAC5CxH,OAAS,SAAC9H,EAAM8J,UAAc,IAAI9M,GAAcgD,EAAM8J,OACtD6O,QAAU,SAAA2N,UAAQA,EAAO1X,MAAe9P,GAAgB9B,GAAc+K,aAAeqH,IAAY,OACjG5T,OAAS,SAAAC,WAAWjD,GAAWC,OAAS6V,GAAqB,IAAV7S,EAAiB,EAAI,OACxE8qB,kBAAoBxW,MACpByW,UAAY,SAACnvB,EAASwhB,UAAevP,GAAWjS,EAASwhB,EAAazZ,GAAcvE,QACpF4rB,cAAgB,SAACpvB,EAASwhB,UAAeve,EAAeX,EAAWtC,GAAUwhB,EAAazZ,GAAcvE,QACxGqN,QAAU,SAAAzE,UAAMyU,GAAKzU,OACrBuE,OAAS,kBAAM8H,GAAUtI,OAAO,SAAA5N,SAAmB,mBAAdA,EAAEoG,KAAKyD,SAC5CijB,YAAc,mBAAQtY,OACtBuY,gBAAkB5b,MAClB9S,iBAAmB,SAACJ,EAAM8T,OACnC9O,EAAIsb,EAAWtgB,KAAUsgB,EAAWtgB,GAAQ,KAC/CgF,EAAErF,QAAQmU,IAAa9O,EAAE/B,KAAK6Q,OAElBvT,oBAAsB,SAACP,EAAM8T,OACtC9O,EAAIsb,EAAWtgB,GAClB+C,EAAIiC,GAAKA,EAAErF,QAAQmU,GACf,GAAL/Q,GAAUiC,EAAE+K,OAAOhN,EAAG,OAETgsB,MAAQ,SAACrH,EAASvf,GAKd,SAAhB6mB,GAAiBhvB,EAAM8T,OAClBmb,EAAW,GACdC,EAAW,GACXlI,EAAQ5nB,GAAKyP,YAAYsgB,EAAU,WAAOrb,EAASmb,EAAUC,GAAWD,EAAW,GAAIC,EAAW,KAAM3gB,eAClG,SAAAvM,GACNitB,EAAS7rB,QAAU4jB,EAAM1Z,SAAQ,GACjC2hB,EAAShsB,KAAKjB,EAAK+V,SACnBmX,EAASjsB,KAAKjB,GACdotB,GAAYH,EAAS7rB,QAAU4jB,EAAM7U,SAAS,QAGhD3K,EAfG8K,EAAS,GACZ+c,EAAW,GACXF,EAAWhnB,EAAKgnB,UAAY,KAC5BC,EAAWjnB,EAAKinB,UAAY,QAaxB5nB,KAAKW,EACTknB,EAAS7nB,GAAyB,OAAnBA,EAAEgJ,OAAO,EAAG,IAAeqB,GAAY1J,EAAKX,KAAa,kBAANA,EAAyBwnB,GAAcxnB,EAAGW,EAAKX,IAAMW,EAAKX,UAEzHqK,GAAYud,KACfA,EAAWA,IACXrvB,GAAaoF,GAAe,UAAW,kBAAMiqB,EAAWjnB,EAAKinB,cAE9D1R,GAASgK,GAAS3T,QAAQ,SAAA1Q,OACrBhB,EAAS,OACRmF,KAAK6nB,EACThtB,EAAOmF,GAAK6nB,EAAS7nB,GAEtBnF,EAAO0V,QAAU1U,EACjBiP,EAAOrP,KAAKkC,GAAc8K,OAAO5N,MAE3BiQ,GAKmC,SAAvCgd,GAAwCrb,EAAYuI,EAASrB,EAAK/W,UAC1DA,EAAVoY,EAAgBvI,EAAW7P,GAAOoY,EAAU,GAAKvI,EAAW,GAC/C7P,EAAN+W,GAAa/W,EAAMoY,IAAYrB,EAAMqB,GAAWrB,EAAM,EAAIqB,GAAWA,EAAUrB,GAAO,EAExE,SAAtBoU,GAAuBlsB,EAAQoQ,IACZ,IAAdA,EACHpQ,EAAOyS,MAAM4F,eAAe,gBAE5BrY,EAAOyS,MAAM0Z,aAA4B,IAAd/b,EAAqB,OAASA,EAAY,OAASA,GAAanN,EAASC,QAAU,cAAgB,IAAM,OAErIlD,IAAWP,IAAUysB,GAAoBvpB,GAAOyN,GAGjC,SAAhBgc,UAGqB5W,EAHHjQ,IAAAA,MAAOvF,IAAAA,OAAQgJ,IAAAA,KAC5BqjB,GAAQ9mB,EAAM9D,eAAiB8D,EAAM9D,eAAe,GAAK8D,GAAOvF,OACnEzC,EAAQ8uB,EAAK3W,OAAS3Z,GAAKgG,KAAK6W,SAASyT,GACzC5U,EAAOjX,SACHjD,EAAM+uB,YAAwC,IAA1B7U,EAAOla,EAAM+uB,WAAmB,MACjDD,GAAQA,IAAS1pB,KAAW0pB,EAAKE,cAAgBF,EAAKG,cAAgBH,EAAKI,aAAeJ,EAAKhZ,cAAkBqZ,IAAWlX,EAAKpG,GAAkBid,IAAOM,aAAcD,GAAUlX,EAAGoX,aAAcP,EAAOA,EAAK3V,WACtNnZ,EAAMsvB,UAAYR,GAAQA,IAASrsB,IAAWzD,GAAY8vB,KAAUK,IAAWlX,EAAKpG,GAAkBid,IAAOM,YAAcD,GAAUlX,EAAGoX,YACxIrvB,EAAM+uB,WAAa7U,GAEhBla,EAAMsvB,WAAsB,MAAT7jB,IACtBzD,EAAMunB,kBACNvnB,EAAM/D,YAAa,GAIJ,SAAjBurB,GAAkB/sB,EAAQrD,EAAMqwB,EAAQC,UAAWhqB,EAAS2J,OAAO,CAClE5M,OAAQA,EACRlD,SAAS,EACToI,UAAU,EACViC,UAAU,EACVxK,KAAMA,EACNkK,QAAUomB,EAASA,GAAUb,GAC7BzmB,QAASsnB,EACTvnB,OAAQunB,EACRtjB,SAAUsjB,EACVnmB,SAAU,2BAAMkmB,GAAUtwB,GAAa6C,GAAM0D,EAASQ,WAAW,GAAIypB,IAAgB,GAAO,IAC5FnmB,UAAW,4BAAM9J,GAAgBsC,GAAM0D,EAASQ,WAAW,GAAIypB,IAAgB,MAWzD,SAAvBC,GAAuBroB,GAoBH,SAAlBsoB,YAAwBC,GAAgB,EAGzB,SAAfC,KACCC,EAAOnf,GAAWpO,EAAQL,IAC1B6tB,EAAelT,GAAOQ,EAAa,EAAI,EAAGyS,GAC1CE,IAAqBC,EAAepT,GAAO,EAAGlM,GAAWpO,EAAQkE,MACjEypB,EAAgBvQ,GAEK,SAAtBwQ,KACC/I,EAAQnP,MAAMhN,EAAI+E,GAAO/C,WAAWma,EAAQnP,MAAMhN,GAAKmB,EAAYrL,QAAU,KAC7EqmB,EAAQpS,MAAMob,UAAY,mDAAqDnjB,WAAWma,EAAQnP,MAAMhN,GAAK,UAC7GmB,EAAYrL,OAASqL,EAAYvL,QAAU,EAqBjC,SAAXwvB,KACCR,KACI3T,EAAMkK,YAAclK,EAAM7U,KAAKiF,QAAUwjB,IAC5C1jB,IAAgB0jB,EAAO5T,EAAM7K,SAAS,IAAMjF,EAAY0jB,GAAQ5T,EAAM4O,QAAQ,UAAWgF,IAvD5F7e,GAAU5J,KAAUA,EAAO,IAC3BA,EAAKvD,eAAiBuD,EAAK4B,aAAe5B,EAAKoC,aAAc,EAC7DpC,EAAKnI,OAASmI,EAAKnI,KAAO,eAC1BmI,EAAKI,WAAaJ,EAAKI,SACvBJ,EAAKyD,GAAKzD,EAAKyD,IAAM,iBAEpB5J,EAAM4uB,EAWNI,EAAeN,EAkCf1T,EAAOoU,EAAcC,EAAchkB,EA9C/ByjB,EAA4D3oB,EAA5D2oB,iBAAkBQ,EAA0CnpB,EAA1CmpB,SAAUC,EAAgCppB,EAAhCopB,kBAAmBtoB,EAAad,EAAbc,UAEnD5F,EAASvB,EAAWqG,EAAK9E,SAAWP,GACpC0uB,EAAWpyB,GAAKgG,KAAKC,UAAUosB,eAC/BC,EAAmBF,GAAYA,EAASG,MACxCzJ,EAAU/J,IAAgBhW,EAAK+f,SAAWpmB,EAAWqG,EAAK+f,UAAcwJ,IAAqC,IAAjBvpB,EAAK+f,UAAsBwJ,EAAiBpuB,UAAYouB,EAAiBxJ,WACrKhb,EAAczK,EAAeY,EAAQL,IACrCiK,EAAcxK,EAAeY,EAAQkE,IACrC+X,EAAQ,EACRsS,GAAgBtrB,EAASC,SAAWpF,GAAK0wB,eAAiB1wB,GAAK0wB,eAAevS,MAAQne,GAAK0wB,eAAexgB,MAAQlQ,GAAK2wB,YAAc3wB,GAAKmQ,WAC1IygB,EAAe,EACfC,EAA0BngB,GAAYyf,GAAY,kBAAMA,EAAStvB,IAAQ,kBAAMsvB,GAAY,KAE3FW,EAAgB7B,GAAe/sB,EAAQ8E,EAAKnI,MAAM,EAAMuxB,GAExDR,EAAelgB,GACfggB,EAAehgB,UAqChBqX,GAAW9oB,GAAKsd,IAAIwL,EAAS,CAACnc,EAAG,QACjC5D,EAAK2B,YAAc,SAAAnF,UAAMwZ,GAAyB,cAAXxZ,EAAE3E,MA1B3B,SAAbkyB,gBACKxB,EAAe,CAClBvkB,sBAAsBskB,QAClB5uB,EAASiP,GAAO9O,EAAKmJ,OAAS,GACjCmP,EAASuW,EAAa3jB,EAAY3L,EAAIM,MACnCqmB,GAAW5N,IAAWpN,EAAY3L,EAAI2L,EAAYrL,OAAQ,CAC7DqL,EAAYrL,OAASyY,EAASpN,EAAY3L,MACtCwK,EAAI+E,IAAQ/C,WAAWma,GAAWA,EAAQnP,MAAMhN,IAAM,GAAKmB,EAAYrL,QAC3EqmB,EAAQpS,MAAMob,UAAY,mDAAqDnlB,EAAI,UACnFmc,EAAQnP,MAAMhN,EAAIA,EAAI,KACtBmB,EAAYvL,QAAUhB,GAAWC,MACjC6V,WAEM,EAERvJ,EAAYrL,QAAUovB,KACtBP,GAAgB,EAU+CwB,IAA2B,KAAR5S,GAA2B,eAAX3a,EAAE3E,MAA0BgC,EAAK8K,aAAgBnI,EAAEkI,SAA8B,EAAnBlI,EAAEkI,QAAQzJ,QAC5K+E,EAAKa,QAAU,WACd0nB,GAAgB,MACZyB,EAAY7S,EAChBA,EAAQxO,IAAS3P,GAAK0wB,gBAAkB1wB,GAAK0wB,eAAevS,OAAU,GAAKsS,GAC3E5U,EAAMzO,QACN4jB,IAAc7S,GAASiQ,GAAoBlsB,EAAgB,KAARic,IAAsBwR,GAA2B,KACpGM,EAAenkB,IACfokB,EAAenkB,IACfyjB,KACAK,EAAgBvQ,IAEjBtY,EAAKc,UAAYd,EAAK6B,eAAiB,SAAChI,EAAM2M,MAC7CzB,EAAYrL,QAAUovB,KACjBtiB,EAEE,CACNhO,GAAWC,YAGVwxB,EAAe7K,EADZ8K,EAAML,IAENlB,IAEHvJ,GADA6K,EAAgBnlB,KACmB,IAANolB,GAAcrwB,EAAKswB,UAAa,KAC7DD,GAAO/C,GAAqCriB,EAAamlB,EAAe7K,EAAW9V,GAAWpO,EAAQkE,KACtGyV,EAAM7U,KAAKgF,QAAU4jB,EAAaxJ,IAGnCA,GADA6K,EAAgBllB,KACmB,IAANmlB,GAAcrwB,EAAKuwB,UAAa,KAC7DF,GAAO/C,GAAqCpiB,EAAaklB,EAAe7K,EAAW9V,GAAWpO,EAAQL,KACtGga,EAAM7U,KAAKiF,QAAUyjB,EAAatJ,GAClCvK,EAAMiM,aAAahW,SAASof,GAAKG,KAAK,MAClCrU,GAAcnB,EAAM7U,KAAKiF,SAAWwjB,GAAyBA,EAAK,GAAtBwB,IAC/ChzB,GAAKqe,GAAG,GAAI,CAACF,SAAU4T,GAAUle,SAAUof,SAlB5ChlB,EAAkBC,SAAQ,GAqB3BrE,GAAaA,EAAUjH,IAExBmG,EAAK+B,QAAU,WACd8S,EAAMyV,KAAOzV,EAAMzO,QACa,IAA5B1K,KAAakuB,IAChBf,EAAgB,EAChBe,EAAeluB,OAGjBsE,EAAKqB,SAAW,SAACxH,EAAMgJ,EAAIE,EAAIwnB,EAAQC,MACtClS,KAAeuQ,GAAiBL,KAChC3lB,GAAM8lB,GAAoB7jB,EAAY8jB,EAAa2B,EAAO,KAAO1nB,EAAKomB,GAAgBpvB,EAAK0K,OAAS1K,EAAK8J,GAAKmB,IAAgBjC,EAAK0nB,EAAO,KACtIxnB,EAAI,CACPgC,EAAYrL,QAAUovB,SAClB1qB,EAAUosB,EAAO,KAAOznB,EAC3Ba,EAAIxF,EAAU8qB,EAAervB,EAAK2K,OAAS3K,EAAK+J,EAAImB,IAAgBhC,EAAKynB,EAAO,GAChFC,EAAW/B,EAAa9kB,GACzBxF,GAAWwF,IAAM6mB,IAAavB,GAAgBuB,EAAW7mB,GACzDmB,EAAY0lB,IAEZ1nB,GAAMF,IAAOyL,KAEftO,EAAKgC,SAAW,WACfolB,GAAoBlsB,GAAQytB,GAA2B,KACvD3rB,GAAc/E,iBAAiB,UAAW+wB,IAC1CpxB,GAAaoB,GAAM,SAAUgwB,IACzBjkB,EAAY5J,SACf4J,EAAY7J,OAAOyS,MAAM+K,eAAiB,OAC1C3T,EAAY5J,OAAS2J,EAAY3J,QAAS,GAE3C2uB,EAAcziB,UAEfrH,EAAKiC,UAAY,WAChBmlB,GAAoBlsB,GAAQ,GAC5B/C,GAAgBa,GAAM,SAAUgwB,IAChChsB,GAAc5E,oBAAoB,UAAW4wB,IAC7Cc,EAAcpiB,QAEf1H,EAAKqC,UAA6B,IAAlBrC,EAAKqC,WACrBxI,EAAO,IAAIsE,EAAS6B,IACfzG,IAAMyc,KACIjR,KAAiBA,EAAY,GAC5CiR,GAAc/e,GAAKyzB,OAAO9K,IAAIlX,IAC9BxD,EAAoBrL,EAAKsN,IACzB0N,EAAQ5d,GAAKqe,GAAGzb,EAAM,CAACokB,KAAM,SAAUE,QAAQ,EAAMnZ,QAAS2jB,EAAmB,QAAU,MAAO1jB,QAAS,QAAS8P,UAAW,CAAC9P,QAAS8O,GAAqBhP,EAAaA,IAAe,kBAAM8P,EAAMzO,WAAYgP,SAAU9G,EAAYwG,WAAY5P,EAAkBlF,KAAK8U,aACpQjb,EA/LT,IA0CC8wB,GA9BA/C,GAAY,CAACgD,KAAM,EAAGzY,OAAQ,GA6B9B0Y,GAAY,iCAEZzC,GAAiB,SAAjBA,eAAiB5rB,OACZsuB,EAAUD,GAAUhX,KAAKrX,EAAEtB,OAAO6vB,UAClCD,GAAWH,MACdnuB,EAAEE,YAAa,EACfiuB,GAAkBG,OAmJP1f,KAAO,SAAAtT,UAAQgY,GAAU1E,KAAKtT,GAAS,SAAC+E,EAAGwO,UAAuC,KAAhCxO,EAAEmD,KAAK8d,iBAAmB,GAAYjhB,EAAEkW,OAAS1H,EAAE0H,OAAyC,KAAhC1H,EAAErL,KAAK8d,iBAAmB,UACxIkN,QAAU,SAAAhrB,UAAQ,IAAI7B,EAAS6B,OAC/BirB,gBAAkB,SAAAjrB,WACV,IAAVA,SACH1H,MAEK,IAAT0H,GAAiB1H,SACbA,EAAY+O,aAEP,IAATrH,SACI1H,GAAeA,EAAYoP,WAE/BwjB,EAAalrB,aAAgB7B,EAAW6B,EAAOqoB,GAAqBroB,UACxE1H,GAAeA,EAAY4C,SAAWgwB,EAAWhwB,QAAU5C,EAAYoP,OACvEjQ,GAAYyzB,EAAWhwB,UAAY5C,EAAc4yB,GAC1CA,MAIMjuB,KAAO,CACpB5B,iBAAAA,EACA4sB,eAAAA,GACAzvB,WAAAA,GACAjB,SAAAA,GACA8F,OAAQ,CAEP8tB,GAAI,cACH/c,IAAmBI,EAAU,eAC7BJ,GAAkB1S,MAGnB0vB,IAAK,sBAAMvc,YAIC5X,GAAKE,eAAe6F"}