@import 'variable';
@import 'mixin';
@import 'fonts';

// Colors
:root {
  @each $key, $color in $colors {
    --color-#{$key}: #{$color};
  }
}

// Club primary colors
:root {
  @each $key, $color in $club-colors {
    --color-club-#{$key}: #{$color};
  }
}

// Club secondary colors
:root {
  @each $key, $color in $club-secondary-colors {
    --color-club-#{$key}-2: #{$color};
  }
}

// Club text colors
:root {
  @each $key, $color in $club-text-colors {
    --color-club-#{$key}-text: #{$color};
  }
}

// Font families
:root {
  @each $key, $value in $fonts {
    --font-#{$key}: #{$value};
  }
}

// Sizes
:root {
  @each $key, $value in $sizes {
    --size-#{$key}: #{$value}px;
  }
}
