@import 'setup/variable';
@import 'setup/mixin';

* {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;

  // &:focus {
  //   outline: none;
  // }
}

// header {
//   overflow: hidden;
// }

body {
  font-family: $font-barlow-condensed;
  font-size: 16px;
  overflow-x: hidden;
  scroll-behavior: smooth;

  &.lang-id {
    @include font-bahasa();
  }

  &.lang-th {
    @include font-thai();
  }

  &.lang-vi {
    @include font-vietnamese();
  }
}

:root {
  body {
    &.lang-th {
      @each $key, $value in $fonts {
        @if ($key != 'noto-sans-thai') {
          --font-#{$key}: #{$font-noto-sans-thai};
        }
      }
    }

    &.lang-vi {
      --font-overpass: #{$font-barlow-condensed};
      --font-druk-wide: #{$font-barlow-condensed};
    }
  }
}

a {
  color: $color-blue;
  text-decoration: none;
  -webkit-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;

  &.no-decoration,
  &.no-decoration:active,
  &.no-decoration:focus,
  &.no-decoration:hover,
  &.no-decoration:visited {
    text-decoration: none;
    // outline: none;
  }

  &:hover {
    opacity: 0.4;
  }
}

/*
  Usecase: image or video in wagtail richtext
*/
.responsive-object {
  position: relative;

  iframe,
  object,
  embed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
