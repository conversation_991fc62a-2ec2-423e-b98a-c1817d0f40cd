@import 'setup/variable';
@import 'setup/mixin';

.language-chooser {
  position: relative;
}

.language-button {
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 1px 7px 3px;

  .arrow,
  .icon {
    pointer-events: none;
  }

  .arrow {
    margin-left: 10px;
  }

  .icon {
    margin-right: 4px;
  }

  .current-language {
    display: inline-block;
    font-family: $font-barlow-condensed;
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    line-height: 24px;
    letter-spacing: 2px;
    color: $color-white;
    text-transform: uppercase;
    white-space: nowrap;
  }

  &:hover {
    opacity: 1;
    background: $color-white;
    border-radius: 1px;

    .current-language {
      color: $color-black;
    }

    .arrow,
    .icon {
      filter: invert(100%);
    }
  }
}

.language-choices {
  padding: 8px 0;
  background-color: $color-black;
  width: 250px;
  max-height: 0;
  opacity: 0;
  position: absolute;
  top: calc(100% - -12px);
  right: 0;
  z-index: 9999;
  box-shadow: 0px 0px 5px 0px $color-black;
  transition: all 0.3s linear;
  pointer-events: none;

  &.language-choices--opened {
    max-height: 500px;
    height: auto;
    opacity: 1;
    pointer-events: auto;
  }

  .language-choice {
    padding: 8px 16px;
    color: $color-white;
    font-family: $font-barlow-condensed;
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 2px;
    text-align: center;
    text-transform: uppercase;
    white-space: nowrap;
    cursor: pointer;

    &.language-choice--selected {
      background-color: $color-white;
      color: $color-black;
    }

    &:hover:not(.language-choice--selected) {
      background-color: transparentize($color: $color-white, $amount: 0.9);
    }
  }

  form {
    height: 0;
    width: 0;
    opacity: 0;
    display: none;
  }
}

body.lang-th {
  .language-button .current-language,
  .language-choices .language-choice {
    @include font-thai();
  }
}
