@import 'setup/variable';
@import 'setup/mixin';

.football-field-player {
  flex: 1;
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 25%;
  position: relative;

  .football-field-player__body {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover {
      opacity: 0.8;
    }
  }

  .player-marker {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    position: relative;

    .player-avatar {
      border: solid 2px $color-white;
      border-radius: 50%;
      height: 48px;
      width: 48px;
      overflow: hidden;

      .player-photo {
        height: 100%;
        width: 100%;
      }
    }

    .player-uniform-no {
      position: absolute;
      bottom: 0;
      left: 0;
      border-radius: 50%;
      transform: translate(40px, -100%);
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;

      .team-text {
        font-family: $font-jleague-kick;
        font-weight: 700;
        font-size: 16px;
        line-height: 16px;
      }
    }

    .player-event {
      position: absolute;
      width: 24px;
      height: 24px;
      border-radius: 50%;

      img {
        height: auto;
        width: 100%;
      }

      &.player-event--in-out {
        bottom: 0;
        left: 0;
        transform: translate(32px, 0);
      }

      &.player-event--scored {
        bottom: 0;
        left: 0;
        transform: translate(-16px, -100%);
      }

      &.player-event--warning {
        bottom: 0;
        left: 0;
        transform: translate(-8px, 2px);
      }
    }
  }

  .player-name {
    color: $color-white;
    font-family: $font-jleague-kick;
    font-weight: 700;
    font-size: 16px;
    line-height: 16px;
    margin-top: 2px;
    white-space: nowrap;
    align-self: center;
    text-align: center;
    text-overflow: ellipsis;
    letter-spacing: 0.2pt;
    overflow: hidden;
    max-width: 130px;
  }

  &.football-field-player--full-width {
    max-width: none;
  }

  &.football-field-player--standalone {
    .player-marker {
      .player-avatar {
        height: 64px;
        width: 64px;
      }

      .player-uniform-no {
        transform: translate(50px, -135%);
      }

      .player-event {
        &.player-event--in-out {
          transform: translate(44px, 0);
        }

        &.player-event--scored {
          transform: translate(-12px, -135%);
        }

        &.player-event--warning {
          transform: translate(-4px, 0px);
        }
      }
    }
  }

  &.football-field-player--left-wing,
  &.football-field-player--right-wing {
    max-width: none;
  }

  &.football-field-player--left-wing {
    align-items: flex-start;
    margin-left: 20px;
  }

  &.football-field-player--right-wing {
    align-items: flex-end;
    margin-right: 20px;
  }
}

@media screen and (max-width: 576px) {
  .football-field-player {
    .player-marker {
      .player-avatar {
        height: 40px;
        width: 40px;
      }

      .player-uniform-no {
        height: 20px;
        width: 20px;
        transform: translate(32px, -100%);

        .team-text {
          font-size: 14px;
          line-height: 11px;
        }
      }

      .player-event {
        width: 16px;
        height: 16px;

        &.player-event--in-out {
          transform: translate(24px, 0);
        }

        &.player-event--scored {
          transform: translate(-10px, -100%);
        }

        &.player-event--warning {
          transform: translate(-8px, 1px);
        }
      }
    }

    .player-name {
      font-size: 14px;
      line-height: 14px;
      max-width: 80px;
    }

    &.football-field-player--standalone {
      .player-marker {
        .player-avatar {
          height: 48px;
          width: 48px;
        }

        .player-uniform-no {
          transform: translate(36px, -180%);
        }

        .player-event {
          &.player-event--in-out {
            transform: translate(36px, 0);
          }

          &.player-event--scored {
            transform: translate(-6px, -145%);
          }

          &.player-event--warning {
            transform: translate(-4px, 0px);
          }
        }
      }
    }
  }
}

@media screen and (max-width: 450px) {
  .football-field-player {
    .player-marker {
      .player-avatar {
        height: 32px;
        width: 32px;
      }

      .player-uniform-no {
        height: 16px;
        width: 16px;
        transform: translate(26px, -100%);

        .team-text {
          font-size: 12px;
          line-height: 9px;
        }
      }
    }

    .player-name {
      font-size: 12px;
      line-height: 12px;
      max-width: 80px;
    }
  }
}

body.lang-th {
  .football-field-player {
    .player-name {
      @include font-thai();
      font-weight: 700;
    }
  }
}
