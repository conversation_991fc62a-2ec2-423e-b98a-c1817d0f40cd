.competition-dropdown {
  position: relative;
  display: inline-block;
  z-index: 99;

  .competition-dropdown__value {
    display: flex;
    align-items: center;

    .competition-logo {
      margin-right: 10px;
      display: none;
      opacity: 0;
      transition: all 0.4s ease-in;

      &.competition-logo--selected {
        display: block;
        opacity: 1;
      }
    }

    &:hover {
      cursor: pointer;
    }
  }

  .competition-dropdown__items {
    position: absolute;
    top: -8px;
    right: -8px;
    visibility: hidden;
    height: 1px;
    transition: all 0.4s ease-in-out;

    &.competition-dropdown__items--visible {
      height: 100%;
      visibility: visible;
    }

    .dropdown-list {
      background-color: #fff;
      border-radius: 3px;
      padding: 8px 0;

      .dropdown-item {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        width: 168px;
        height: 36px;
        overflow: hidden;

        .competition-label {
          color: #1a1919;
          font-family: 'Overpass';
          font-size: 16px;
          font-weight: 900;
          line-height: 20px;
          letter-spacing: 0px;
          margin-left: 10px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &:hover {
          background-color: #ede2da;
          cursor: pointer;
          opacity: 1;
        }

        &.dropdown-item--selected {
          background-color: #ede2da;
          cursor: not-allowed;
        }
      }
    }
  }
}
