@import 'setup/variable';
@import 'setup/mixin';

.competitions-list {
  margin-top: 40px;
  max-width: 100%;
  display: flex;
  flex-wrap: nowrap;
  // overflow-x: auto;
  // overflow-y: hidden;
  // @include scrollbars();

  .competitions-list__item {
    background: $color-white;
    border: none;
    border-bottom: solid 8px $color-earth-2;
    color: $color-black;
    font-family: $font-overpass;
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 32px;
    white-space: nowrap;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    height: 56px;
    padding: 0 24px;

    .competition-name {
      margin-left: 10px;
    }

    &.competitions-list__item--selected {
      border-color: $color-red;
      color: $color-red;
    }

    &:hover {
      background: $color-earth-2;
      border-bottom-color: $color-red;
      opacity: 1;
    }
  }

  &.competitions-list--homepage {
    margin-top: 0;
    background-color: $color-white;
    .competitions-list__item {
      height: 40px;
      border-bottom-width: 4px;
      padding: 0 16px;
      font-size: 16px;
      line-height: 20px;
    }
  }

  @media screen and (max-width: 767px) {
    .competitions-list__item {
      font-size: 16px;
      line-height: 20px;
      border-bottom-width: 4px;
    }
  }

  @media screen and (max-width: 425px) {
    margin-top: 0;
  }
}

body.lang-th {
  .competitions-list {
    .competitions-list__item {
      @include font-thai();
    }
  }
}
