@import 'setup/variable';
@import 'setup/mixin';

.dropdown {
  position: relative;
  display: inline-block;
  background: $color-white;
  border: 1px solid $color-drawn;
  border-radius: 4px;
  height: 48px;

  .dropdown__selector {
    height: 100%;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: transparent;
    border: none;
    // outline: none;
    color: $color-black;
    font-family: $font-overpass;
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
    padding: 0 52px 0 20px;

    &:disabled {
      cursor: not-allowed;
    }

    &:not(:disabled) {
      cursor: pointer;
    }
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-image: url('/static/images/icons/chevron-down-1A1919.svg');
    background-repeat: no-repeat;
    background-size: contain;
    pointer-events: none;
    right: 20px;
    width: 16px;
    height: 9px;
  }

  &.dropdown--disabled {
    border-color: lighten($color: $color-drawn, $amount: 35);
    opacity: 0.5;

    &::after {
      opacity: 0.4;
    }
  }

  &:hover {
    .dropdown__selector {
      &:not(:disabled) {
        background-color: $color-earth-1;
      }
    }
  }

  &.secondary {
    background: transparent;
    border-color: $color-white;

    .dropdown__selector {
      color: $color-white;

      option {
        color: $color-black;
      }
    }

    &::after {
      background-image: url('/static/images/icons/chevron-down-FFFFFF.svg');
    }

    &:hover {
      opacity: 0.8;
    }
  }

  @media screen and (max-width: 1199px) {
    &::after {
      right: 16px;
      width: 12px;
      height: 7px;
    }
  }

  @media screen and (max-width: 767px) {
    height: 36px;

    select,
    .dropdown__selector {
      font-size: 16px;
      line-height: 20px;
      padding: 0 44px 0 16px;
      width: 100%;
    }
  }
}

body.lang-th {
  .dropdown .dropdown__selector {
    @include font-thai();
  }
}

body.lang-vi {
  .dropdown .dropdown__selector {
    @include font-vietnamese();
  }
}
