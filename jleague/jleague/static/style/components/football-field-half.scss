$arch-area-height: 8%;
$arch-area-width: 20%;
$center-circle-size: 35%;
$penalty-area-height: 25%;
$penalty-area-width: 50%;
$goal-area-height: 10%;
$goal-area-width: 25%;
$field-height: 480px; // 452;
$field-width: 100%; // 342;
$field-color-1: #255742;
$field-color-2: #1c4d38;
$line-color: rgba(255, 255, 255, 0.3);
$line-width: 2px;

.football-field-half {
  height: auto;
  width: 100%;

  .formation-detail {
    background-color: #f5ede8;
    color: #1a1919;
    font-family: 'Overpass';
    font-style: normal;
    font-size: 20px;
    font-weight: 900;
    text-align: center;
    line-height: 20px;
    padding: 16px 16px 13px;
  }

  .field {
    // background: repeating-linear-gradient(
    //   0deg,
    //   $field-color-1,
    //   $field-color-1 20%,
    //   $field-color-2 0,
    //   $field-color-2 40%
    // );
    border: $line-width solid $line-color;
    height: $field-height;
    width: $field-width;
    overflow: hidden;
    position: relative;

    .field__grass {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      background-color: $field-color-1;

      div {
        flex: 1;

        // &:nth-child(even) {
        //   background-color: $field-color-1;
        // }

        &:nth-child(odd) {
          background-color: $field-color-2;
        }
      }
    }

    .field__center-area {
      border: $line-width solid $line-color;
      border-radius: 50%;
      position: absolute;
      z-index: 3;
      height: $center-circle-size;
      width: $center-circle-size;
      left: 50%;
      top: 0;
      transform: translate(-50%, -50%);
    }

    .field__arch-area {
      border: $line-width solid $line-color;
      border-radius: 50% 50% 0 0/100% 100% 0 0;
      height: $arch-area-height;
      width: $arch-area-width;
      position: absolute;
      z-index: 3;
      bottom: calc(#{$penalty-area-height} - (2 * #{$line-width}));
      left: 50%;
      transform: translate(-50%, 0);
    }

    .field__goal-box {
      border: $line-width solid $line-color;
      height: $goal-area-height;
      width: $goal-area-width;
      position: absolute;
      z-index: 3;
      bottom: -#{$line-width};
      left: 50%;
      transform: translate(-50%, 0);
    }

    .field__penalty-area {
      border: $line-width solid $line-color;
      height: $penalty-area-height;
      width: $penalty-area-width;
      position: absolute;
      z-index: 3;
      left: 25%;
      bottom: -#{$line-width};
    }

    .field__corners {
      height: 100%;
      overflow: hidden;
      position: relative;
      width: 100%;

      .field__corner {
        border: $line-width solid $line-color;
        border-radius: 50%;
        height: 30px;
        position: absolute;
        z-index: 3;
        width: 30px;

        &.field__corner--tl {
          left: -15px;
          top: -15px;
        }

        &.field__corner--tr {
          right: -15px;
          top: -15px;
        }

        &.field__corner--bl {
          left: -15px;
          bottom: -15px;
        }

        &.field__corner--br {
          right: -15px;
          bottom: -15px;
        }
      }
    }

    .field__team-area {
      height: 100%;
      width: 100%;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      flex-direction: column-reverse;
      justify-content: space-evenly;
    }
  }

  .player-row {
    display: flex;
    height: 20%;
    align-items: center;
    justify-content: center;
  }

  @media screen and (max-width: 576px) {
    .field {
      height: 400px;
    }
  }

  @media screen and (max-width: 450px) {
    .field {
      height: 350px;
    }
  }
}
