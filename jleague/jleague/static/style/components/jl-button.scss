@import 'setup/mixin';
@import 'setup/variable';

.jl-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  height: 48px;
  background: $color-white;
  border: 1px solid $color-black;
  border-radius: 25px;
  transition: all 0.3s linear;

  .jl-button__label {
    color: $color-black;
    font-family: $font-barlow-condensed;
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 24px;
    text-align: center;
    leading-trim: both;
    text-edge: cap;
    font-feature-settings: 'clig' off, 'liga' off;
    letter-spacing: 1.667px;
    text-transform: uppercase;
    white-space: nowrap;
    @include text_ellipsis();
    transition: all 0.3s linear;
  }

  &.jl-button--inverse {
    background: $color-black;
    border-color: $color-black;

    .jl-button__label {
      color: $color-white;
    }
  }

  &:hover:not(:disabled) {
    opacity: 1;
    background: $color-red;
    border-color: $color-red;
    cursor: pointer;

    .jl-button__label {
      color: $color-white;
    }
  }

  &.jl-button--red {
    background: $color-j1;
    border-color: $color-j1;

    .jl-button__label {
      color: $color-white;
    }

    &:hover:not(:disabled) {
      background-color: $color-white;
      border-color: $color-black;

      .jl-button__label {
        color: $color-black;
      }
    }
  }

  &.jl-button--white-nobg {
    background: transparent;
    border-color: $color-white;

    .jl-button__label {
      color: $color-white;
    }

    &:hover:not(:disabled) {
      background-color: $color-j1;
      border-color: $color-j1;
    }
  }

  &.jl-button--live-link {
    background-color: $color-j1;
    border-color: $color-j1;

    .jl-button__label {
      color: $color-white;
    }

    &::before {
      content: '';
      transition: all 0.3s linear;
      width: 24px;
      height: 24px;
      display: block;
      background: url('/static/images/icons/play-FFFFFF.svg');
      background-position: center;
      background-repeat: no-repeat;
      background-size: contain;
    }

    &:hover:not(:disabled) {
      background-color: $color-white;
      border-color: $color-white;

      .jl-button__label {
        color: $color-black;
      }

      &::before {
        background: url('/static/images/icons/play-1A1919.svg');
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
      }
    }
  }

  &.jl-button--medium {
    height: 40px;
    padding: 8px 24px;

    .jl-button__label {
      font-size: 16px;
      line-height: 24px;
    }
  }

  &.jl-button--small {
    height: 32px;
    padding: 8px 16px;

    .jl-button__label {
      font-size: 14px;
      line-height: 16px;
    }
  }
}

body.lang-th {
  .jl-button .jl-button__label {
    @include font-thai();
  }
}
