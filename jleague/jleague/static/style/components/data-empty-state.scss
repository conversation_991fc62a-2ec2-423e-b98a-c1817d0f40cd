@import 'setup/variable';
@import 'setup/mixin';

.data-empty-state {
  padding: 0 24px;
  position: relative;
  width: 100%;

  .data-empty-state__icon {
    display: block;
    margin: 24px auto;
  }

  .data-empty-state__text {
    color: $color-earth-4;
    font-family: $font-overpass;
    font-style: normal;
    font-weight: normal;
    font-size: 24px;
    line-height: 28px;
    text-align: center;
  }

  @media screen and (max-width: 767px) {
    .data-empty-state__text {
      font-size: 20px;
    }
  }

  @media screen and (max-width: 576px) {
    .data-empty-state__text {
      font-size: 16px;
    }
  }
}

body.lang-th {
  .data-empty-state .data-empty-state__text {
    @include font-thai();
  }
}

body.lang-vi {
  .data-empty-state .data-empty-state__text {
    @include font-vietnamese();
  }
}
