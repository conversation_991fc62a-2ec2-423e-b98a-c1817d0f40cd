@import 'setup/variable';

.banners {
  position: relative;
  z-index: 9;
  display: flex;
  flex-direction: column;
  gap: 32px;
  // overflow: hidden;

  .banner-item {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 100%;
    width: 592px;
    height: 172px;
    overflow: hidden;
    position: relative;
    transition: opacity 250ms ease-in-out;

    .banner-link {
      display: block;
      width: 100%;
      height: 100%;
      position: relative;

      &:hover {
        opacity: 1;
      }
    }

    // &.swiper-slide-prev,
    // &.swiper-slide-next {
    //   cursor: pointer;

    //   .banner-link {
    //     pointer-events: none;
    //   }
    // }

    &:not(.swiper-slide-active) {
      cursor: pointer;

      .banner-link {
        pointer-events: none;
      }

      &::after {
        content: '';
        background-color: rgba($color: $color-black, $alpha: 0.7);
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
  }

  .banner-img {
    width: 100%;
    height: auto;
    // max-height: 200px;
    // display: none;
    pointer-events: none;

    // &.banner-img--single {
    display: block;
    // }

    // &.banner-img--sm {
    //   max-width: 420px;
    // }

    // &.banner-img--md {
    //   width: 688px;
    // }

    // &.banner-img--lg {
    //   max-width: 1200px;
    // }
  }

  &.banners--top {
    margin-bottom: 48px;
  }

  .swiper-pagination {
    .swiper-pagination-bullet {
      background-color: transparentize($color: $color-white, $amount: 0.25);
      &.swiper-pagination-bullet-active {
        background-color: $color-white;
      }
    }
  }

  // @media screen and (min-width: 1280px) {
  //   .banner-img--md,
  //   .banner-img--sm {
  //     display: none;
  //   }
  //   .banner-img--lg {
  //     display: block;
  //   }
  // }

  @media screen and (min-width: 768px) {
    .banner-img--lg,
    .banner-img--sm {
      display: none;
    }
    .banner-img--md {
      display: block;
    }
  }

  @media screen and (max-width: 767px) {
    .banner-item {
      width: 100%;
      height: auto;
      max-width: 360px;
    }

    .banner-img--lg,
    .banner-img--md {
      display: none;
    }
    .banner-img--sm {
      display: block;
    }
  }
  @media screen and (max-width: 425px) {
    &.banners--top {
      margin-bottom: 40px;
    }
  }
}
