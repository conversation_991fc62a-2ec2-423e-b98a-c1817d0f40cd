.footer {
  background: var(--color-earth-1);
  overflow: hidden;

  .copyright,
  .divider,
  .footer-nav {
    margin-left: auto;
    margin-right: auto;
    max-width: 1440px;
  }

  .footer-nav {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    column-gap: 24px;
    row-gap: 40px;
    padding: 80px 168px 0;
    margin-bottom: 40px;

    .footer-nav__grid {
      .title {
        color: var(--color-earth-4);
        font-family: var(--font-barlow-condensed);
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        letter-spacing: 1.5px;
        line-height: 24px;
        text-transform: uppercase;
        padding-bottom: 16px;
      }

      .nav-list {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .nav-list__item {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          .nav-item-text {
            color: var(--color-black);
            font-family: var(--font-overpass);
            font-feature-settings: 'clig' off, 'liga' off;
            font-style: normal;
            font-weight: 700;
            font-size: 18px;
            line-height: 24px;

            &.nav-item-text--link {
              cursor: pointer;
              text-decoration: none;
            }

            &.nav-item-text--body {
              font-size: 16px;
              font-weight: 400;
              line-height: 120%;
            }
          }
        }
      }
    }
  }

  .subscription-form {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;

    .subscription-input {
      input {
        background: var(--color-white);
        border: 1px solid var(--color-earth-3);
        border-radius: 8px;
        color: var(--color-jl-black);
        font-feature-settings: 'clig' off, 'liga' off;
        font-family: var(--font-overpass);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 120%; /* 19.2px */
        padding: 11px 16px 10px 16px;
        width: 100%;

        &:active,
        &:focus {
          border-color: var(--color-jl-black);
          background: var(--color-white);
          box-shadow: 0px 0px 0px 4px rgba(205, 181, 166, 0.4);
        }

        &:focus-visible {
          outline: none;
        }
      }
    }
  }

  .divider {
    padding: 0 168px;

    &::before {
      content: '';
      display: block;
      height: 2px;
      width: 100%;
      margin-top: 24px;
      background-color: var(--color-earth-3);
    }
  }

  .copyright {
    padding: 0 168px 80px;

    .copyright__text {
      color: var(--color-earth-4);
      font-family: var(--font-barlow-condensed);
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      line-height: 16.8px;
      letter-spacing: 1.5px;
      text-align: center;
      margin-top: 32px;
    }
  }
}

@media screen and (max-width: 1100px) {
  .footer {
    .footer-nav {
      grid-template-areas:
        'logo competition content'
        '.    about       subscribe';
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(2, auto);

      .footer-nav__grid:nth-child(4) {
        grid-area: about;
      }

      .footer-nav__grid:nth-child(5) {
        grid-area: subscribe;
      }
    }
  }
}

@media screen and (max-width: 960px) {
  .footer {
    .footer-nav {
      padding: 80px 32px 0;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(3, auto);
      grid-template-areas: none;

      .footer-nav__grid:nth-child(4),
      .footer-nav__grid:nth-child(5) {
        grid-area: unset;
      }
    }

    .divider {
      padding: 0 32px;
    }

    .copyright {
      padding: 0 32px 80px;
    }
  }
}

@media screen and (max-width: 576px) {
  .footer {
    .footer-nav {
      grid-template-columns: 1fr;
    }
  }
}

body.lang-vi {
  .footer .footer-nav .footer-nav__grid .nav-list .nav-list__item .nav-item-text.nav-item-text--link {
    font-weight: 500;
  }
}
