@import 'setup/variable';
@import 'setup/mixin';

.club-rank-board {
  flex: 1;
  border-top: 1px solid var(--color-j1);
  transition: all 0.5s ease-in-out;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .club-rank-board__header {
    .club-rank-board__header__title {
      margin-top: 24px;
      color: var(--color-black);
      font-family: var(--font-overpass);
      font-style: normal;
      font-weight: 900;
      font-size: 28px;
      line-height: 32px;
    }
  }

  .club-rank-board__body {
    margin-top: 24px;
    flex: 1;
  }

  .ranking-item {
    padding: 0 16px;
    border-bottom: 1px solid var(--color-earth-2);
    height: 96px;
    display: flex;
    align-items: center;

    .rank {
      flex: 0 0 auto;
      width: 16px;
      text-align: center;
      color: var(--color-earth-4);
      font-family: var(--font-jleague-kick);
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 20px;
    }

    .team {
      display: flex;
      align-items: center;
      flex: 1;

      .club-emblem {
        flex: 0 0 auto;
        margin-left: 12px;
        width: 64px;
        height: 64px;
      }

      .team-name {
        flex: auto;
        margin: 0 8px;
        color: var(--color-black);
        font-family: var(--font-overpass);
        font-style: normal;
        font-weight: 900;
        font-size: 18px;
        line-height: 20px;
        @include text_ellipsis();
      }

      &:hover {
        opacity: 1;

        &[href] {
          .team-name {
            text-decoration: underline;
          }
        }
      }
    }

    .value {
      flex: 0 0 auto;
      color: var(--color-black);
      font-family: var(--font-jleague-kick);
      font-style: normal;
      font-weight: 700;
      font-size: 64px;
      line-height: 64px;
    }

    &.ranking-item--winner {
      position: relative;
      background: var(--color-black);

      .rank {
        color: var(--color-earth-3);
      }

      .team {
        .team-name {
          color: var(--color-white);
        }
      }

      .value {
        color: var(--color-white);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 8px;
        background: linear-gradient(90deg, rgba(26, 25, 25, 0.0001) 0%, var(--color-red) 99.69%);
      }
    }

    &.ranking-item--distance,
    &.ranking-item--passes,
    &.ranking-item--sprints,
    &.ranking-item--tackles,
    &.ranking-item--chances_created,
    &.ranking-item--expected_goals_pg,
    &.ranking-item--expected_goals_against_pg {
      .value {
        font-size: 32px;
        line-height: 32px;
      }
    }
  }

  @media screen and (max-width: 1199px) {
    .ranking-item {
      height: 72px;

      .team {
        .club-emblem {
          width: 40px;
          height: 40px;
        }

        .team-name {
          font-size: 20px;
          line-height: 24px;
        }
      }

      .value {
        font-size: 32px;
        line-height: 32px;
      }
    }
  }

  @media screen and (max-width: 767px) {
    .club-rank-board__header {
      .club-rank-board__header__title {
        font-size: 20px;
        line-height: 24px;
      }
    }
  }
}

body.lang-th {
  .club-rank-board {
    .club-rank-board__header .club-rank-board__header__title,
    .ranking-item .team .team-name {
      // @include font-thai();
      font-weight: 700;
      line-height: 1.5;
    }
  }
}

body.lang-vi {
  .club-rank-board .club-rank-board__header .club-rank-board__header__title,
  .ranking-item .team .team-name {
    // @include font-vietnamese();
    font-weight: 600;
  }
}
