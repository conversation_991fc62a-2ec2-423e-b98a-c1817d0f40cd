/* Customize the label (the container) */
.jl-checkbox {
  display: block;
  position: relative;
  padding-left: 20px;
  cursor: pointer;
  font-size: 16px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  height: 20px;

  /* Hide the browser's default checkbox */
  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;

    /* When the checkbox is checked, add a blue background */
    &:checked ~ .checkmark {
      background-color: $color-earth-4;
    }

    &:checked:disabled ~ .checkmark {
      background-color: $color-drawn;
    }
  }

  /* Create a custom checkbox */
  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: $color-white;
    border: solid 1px $color-earth-4;
    color: $color-earth-4;

    /* Create the checkmark/indicator (hidden when not checked) */
    &:after {
      content: '';
      position: absolute;
      display: none;
    }

    /* Style the checkmark/indicator */
    &:after {
      left: 6px;
      top: 3px;
      width: 4px;
      height: 8px;
      border: solid $color-white;
      border-width: 0 2px 2px 0;
      -webkit-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      transform: rotate(45deg);
    }
  }

  &.jl-checkbox--has-label {
    padding-left: 25px;
  }

  &.jl-checkbox--disabled {
    cursor: not-allowed;
  }

  &:hover {
    /* On mouse-over, add a grey background color */
    input:not(:disabled) ~ .checkmark {
      background-color: $color-earth-3;
    }

    input:disabled {
      cursor: not-allowed;
    }
  }

  /* Show the checkmark when checked */
  input:checked ~ .checkmark:after {
    display: block;
  }
}
