@import 'background';
@import 'margins';
@import 'paddings';
@import 'text';

.d-none {
  display: none !important;
}

// copied from bootstrap 5.1 example
.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

hr {
  margin: 32px 0;
  border: 0;
  border-top: dotted 6px $color-earth-2;
}

img {
  transition: opacity 0.5s ease-in-out;

  &.lazy {
    opacity: 0;
    min-height: 100%;
  }
}

.image-wrapper {
  height: auto;
  width: 100%;
  line-height: 0;

  img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }

  &.image-wrapper--ratio-32 {
    aspect-ratio: 3/2;
  }

  &.image-wrapper--ratio-43 {
    aspect-ratio: 4/3;
  }

  &.image-wrapper--ratio-169 {
    aspect-ratio: 16/9;
  }
}

.loading {
  position: relative;
  min-height: 80px;

  & > div,
  & > section {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
  }

  &::after {
    position: absolute;
    content: url('/static/images/loader.gif');
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

body.noscroll {
  overflow: hidden;
}

/* prevent transition while page loading */
.no-transition * {
  -webkit-transition: 0s !important;
  -moz-transition: 0s !important;
  -ms-transition: 0s !important;
  -o-transition: 0s !important;
  transition: 0s !important;
}

/* prevent animation while page loading */
.no-animation * {
  animation-duration: 0s !important;
  -webkit-animation-duration: 0s !important;
}
