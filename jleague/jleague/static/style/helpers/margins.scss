@import 'setup/variable';

@each $size-key, $size-value in $sizes {
  .m-#{$size-key} {
    margin: #{$size-value}px;
  }

  .m-x-#{$size-key} {
    margin-left: #{$size-value}px;
    margin-right: #{$size-value}px;
  }

  .m-y-#{$size-key} {
    margin-bottom: #{$size-value}px;
    margin-top: #{$size-value}px;
  }
}

@each $side-key, $side-value in $sides {
  .m-#{$side-key}-0 {
    margin-#{$side-value}: 0 !important;
  }
}

.m-x-0 {
  margin-left: 0px !important;
  margin-right: 0px !important;
}

.m-y-0 {
  margin-bottom: 0px !important;
  margin-top: 0px !important;
}

@each $side-key, $side-value in $sides {
  @each $size-key, $size-value in $sizes {
    .m-#{$side-key}-#{$size-key} {
      margin-#{$side-value}: #{$size-value}px;
    }
  }
}
