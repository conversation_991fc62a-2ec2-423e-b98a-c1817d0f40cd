@import 'setup/variable';

@each $size-key, $size-value in $sizes {
  .p-#{$size-key} {
    padding: #{$size-value}px;
  }

  .p-x-#{$size-key} {
    padding-left: #{$size-value}px;
    padding-right: #{$size-value}px;
  }

  .p-y-#{$size-key} {
    padding-bottom: #{$size-value}px;
    padding-top: #{$size-value}px;
  }
}

@each $side-key, $side-value in $sides {
  .m-#{$side-key}-0 {
    padding-#{$side-value}: 0 !important;
  }
}

.p-x-0 {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.p-y-0 {
  padding-bottom: 0px !important;
  padding-top: 0px !important;
}

@each $side-key, $side-value in $sides {
  @each $size-key, $size-value in $sizes {
    .p-#{$side-key}-#{$size-key} {
      padding-#{$side-value}: #{$size-value}px;
    }
  }
}
