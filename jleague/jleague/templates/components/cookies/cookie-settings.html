{% load static core_tags %}

<div class="cookie-settings cookie-settings--closed">
  <div class="cookie-settings__content">
    <div class="setting-header">
      <h2 class="title">{% translation 'GLOBAL_COOKIE_SETTINGS_MODAL_TITLE' %}</h2>

      <button type="button" class="close">
        <img
          class="lazy"
          src="{% static 'images/icons/cross-646A78.svg' %}"
          alt=""
          loading="lazy"
        />
      </button>
    </div>

    <div class="setting-body">
      <p>{% translation 'GLOBAL_COOKIE_SETTINGS_MODAL_TEXT' %}</p>

      <ul class="cookie-group">
        <li class="cookie-item accordion" role="button">
          <div class="accordion-header">
            <h3 class="title">
              <span class="icon">
                <img class="icon__closed" src="{% static 'images/icons/plus-6B5B52.svg' %}" />
                <img class="icon__open" src="{% static 'images/icons/minus-6B5B52.svg' %}" />
              </span>
              {% translation 'GLOBAL_COOKIE_STRICTLY_NECESSARY' %}
            </h3>

            <div class="checkbox">
              {% include 'components/checkbox.html' with checked=True disabled=True%}
            </div>
          </div>

          <div class="accordion-body">
            <p>{% translation 'GLOBAL_COOKIE_STRICTLY_NECESSARY_DESCRIPTION' %}</p>
          </div>
        </li>

        <li class="cookie-item accordion" role="button">
          <div class="accordion-header">
            <h3 class="title">
              <span class="icon">
                <img class="icon__closed" src="{% static 'images/icons/plus-6B5B52.svg' %}" />
                <img class="icon__open" src="{% static 'images/icons/minus-6B5B52.svg' %}" />
              </span>
              {% translation 'GLOBAL_COOKIE_FUNCTIONAL' %}
            </h3>

            <div class="checkbox">
              {% include 'components/checkbox.html' with checked=True input_id='functional' %}
            </div>
          </div>

          <div class="accordion-body">
            <p>{% translation 'GLOBAL_COOKIE_FUNCTIONAL_DESCRIPTION' %}</p>
          </div>
        </li>

        <li class="cookie-item accordion" role="button">
          <div class="accordion-header">
            <h3 class="title">
              <span class="icon">
                <img class="icon__closed" src="{% static 'images/icons/plus-6B5B52.svg' %}" />
                <img class="icon__open" src="{% static 'images/icons/minus-6B5B52.svg' %}" />
              </span>
              {% translation 'GLOBAL_COOKIE_PERFORMANCE' %}
            </h3>

            <div class="checkbox">
              {% include 'components/checkbox.html' with input_id='performance'%}
            </div>
          </div>

          <div class="accordion-body">
            <p>{% translation 'GLOBAL_COOKIE_PERFORMANCE_DESCRIPTION' %}</p>
          </div>
        </li>

        <li class="cookie-item accordion" role="button">
          <div class="accordion-header">
            <h3 class="title">
              <span class="icon">
                <img class="icon__closed" src="{% static 'images/icons/plus-6B5B52.svg' %}" />
                <img class="icon__open" src="{% static 'images/icons/minus-6B5B52.svg' %}" />
              </span>
              {% translation 'GLOBAL_COOKIE_TARGETING' %}
            </h3>

            <div class="checkbox">
              {% include 'components/checkbox.html' with input_id='targeting'%}
            </div>
          </div>

          <div class="accordion-body">
            <p>{% translation 'GLOBAL_COOKIE_TARGETING_DESCRIPTION' %}</p>
          </div>
        </li>
      </ul>
    </div>

    <div class="setting-footer">
      <div class="action-buttons">
        <button id="cookie-save" class="jl-button button-save" type="button">
          <span class="jl-button__label">
            {% translation 'GLOBAL_COOKIE_SETTINGS_MODAL_SAVE_BUTTON_LABEL' %}
          </span>
        </button>

        <button id="cookie-accept-all" class="jl-button jl-button--inverse button-accept" type="button">
          <span class="jl-button__label">
            {% translation 'GLOBAL_COOKIE_SETTINGS_MODAL_ACCEPT_BUTTON_LABEL' %}
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
