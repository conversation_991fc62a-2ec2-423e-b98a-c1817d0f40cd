<div class="swiper-slide video-item" data-vid="{{ event.id }}" data-title="{{ event.title }}" data-url="{% url 'match_overview' event.competition.slug event.match.id %}">
  <div class="video-item__header">
    <div class="video-thumbnail">
      <img src="{{ event.thumbnail_file }}" alt="{{ event.title }}" loading="lazy" width="100%" height="100%" />
    </div>
  </div>

  <div class="video-item__body">
    {% if event.team == 'home' %}
      <div class="team-bg team-bg--{{ event.home.slug }}"></div>
    {% elif event.team == 'away' %}
      <div class="team-bg team-bg--{{ event.away.slug }}"></div>
    {% endif %}

    {% include 'components/goal-highlights/video-info.html' with event=event %}

    {% include 'components/score-board.html' with home=event.home away=event.away home_score=event.home_score away_score=event.away_score four_letter_name=True has_border=False custom_class='score-board--video-item' %}
  </div>
</div>
