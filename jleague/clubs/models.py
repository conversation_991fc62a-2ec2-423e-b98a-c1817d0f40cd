from django.db import models
from django_jsonform.models.fields import <PERSON><PERSON><PERSON><PERSON>
from modelcluster.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from wagtail.admin.panels import FieldPanel, InlinePanel, MultiFieldPanel
from wagtail.models import Orderable
from wagtail.search import index
from wagtail.snippets.models import register_snippet
from modelcluster.models import ClusterableModel
from core.constants import LEAGUE_COMPETITIONS, TRANSLATION_VALUES_SCHEMA
from core.enums import TeamMemberTypeEnum
from core.models import Competition
from mascots.models import Mascot
from media.models import Video
from players.models import Player
from stadiums.models import Stadium, StadiumExtraInfo
from utils.helpers import get_country_flag, get_display_data_year


@register_snippet
class Club(models.Model, index.Indexed):
    """
    source file: team-directory-x.xml
    old model: data_stadium.Team
    """

    ds_club_id = models.IntegerField(unique=True, db_index=True)
    name = models.CharField(max_length=255)
    name_short = models.CharField(max_length=255)
    slug = models.SlugField()
    federation_id = models.IntegerField(blank=True, null=True)
    stadium = models.ForeignKey(
        Stadium, blank=True, null=True, on_delete=models.SET_NULL
    )
    club_logo = models.ForeignKey(
        "wagtailimages.Image",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="+",
        help_text="Official logo image of the club",
        verbose_name="Club Logo",
    )
    founded = models.IntegerField(null=True)
    website_url = models.URLField(max_length=255, null=True, blank=True)
    twitter_url = models.URLField(max_length=255, null=True, blank=True)
    facebook_url = models.URLField(max_length=255, null=True, blank=True)
    instagram_url = models.URLField(max_length=255, null=True, blank=True)
    j1_license = models.BooleanField(null=True)
    j2_license = models.BooleanField(null=True)
    j3_license = models.BooleanField(null=True)
    average_age = models.DecimalField(max_digits=3, decimal_places=1, null=True)
    average_height = models.DecimalField(max_digits=4, decimal_places=1, null=True)
    average_weight = models.DecimalField(max_digits=4, decimal_places=1, null=True)
    hometowner_count = models.IntegerField(null=True)
    hometowner_rate = models.DecimalField(max_digits=4, decimal_places=1, null=True)
    domestic_league_count = models.IntegerField(blank=True, null=True)
    domestic_league_rate = models.DecimalField(
        max_digits=4, decimal_places=1, blank=True, null=True
    )
    country_id = models.CharField(max_length=255, null=True)
    country_name = models.CharField(max_length=255, null=True)
    prefecture = models.CharField(max_length=255, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    search_fields = [
        index.SearchField("name", partial_match=True),
    ]

    panels = [
        FieldPanel("name"),
        FieldPanel("name_short"),
        FieldPanel("slug"),
        FieldPanel("website_url"),
        FieldPanel("facebook_url"),
        FieldPanel("instagram_url"),
        FieldPanel("twitter_url"),
        MultiFieldPanel(
            [
                FieldPanel("club_logo"),
            ],
            heading="Logo club",
        ),
        FieldPanel("stadium"),
    ]

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} <id={self.id};ds_club_id={self.ds_club_id};>"

    def get_competition(self, year=None):
        year = year or get_display_data_year()
        competitions = self.club_competition.filter(year=year)
        main_competition = None
        other_competitions = []
        for c in competitions:
            if c.competition.slug in LEAGUE_COMPETITIONS:
                main_competition = c.competition
            else:
                other_competitions.append(c.competition)
        return main_competition, other_competitions

    def get_name_slug(self):
        name_slug = self.name.replace(" ", "-").replace("･", "-").replace(".", "")
        return name_slug

    def get_logo_url(self):
        try:
            if self.club_logo and self.club_logo.file:
                return self.club_logo.file.url
        except AttributeError:
            pass
        return ""

    def is_jleague_club(self):
        licenses = (self.j1_license, self.j2_license, self.j3_license)
        return True in licenses

    def format(self, include_competition=False):
        data = self.__dict__
        data["name_slug"] = self.get_name_slug()
        data["allow_view_profile"] = self.is_jleague_club()
        data["logo_url"] = self.get_logo_url()
        if include_competition:
            competition, _ = self.get_competition()
            if competition:
                data["competition"] = competition.format()
        return data


class ClubExtraInfo(ClusterableModel, index.Indexed):
    title = models.CharField(max_length=255, default="")
    club = models.ForeignKey(
        Club,
        related_name="extra_info",
        default=None,
        null=True,
        on_delete=models.CASCADE,
    )
    address = models.TextField(blank=True, null=True)
    phone_number = models.CharField(max_length=50, blank=True, null=True)
    introduction = models.TextField(blank=True, null=True)
    president = models.CharField(max_length=255, blank=True, null=True)
    practice_ground = models.TextField(blank=True, null=True)
    four_letters_name = models.CharField(max_length=4, blank=True, null=True)
    t_president = JSONField(schema=TRANSLATION_VALUES_SCHEMA, blank=True, null=True)
    t_address = JSONField(schema=TRANSLATION_VALUES_SCHEMA, blank=True, null=True)
    t_practice_ground = JSONField(
        schema=TRANSLATION_VALUES_SCHEMA, blank=True, null=True
    )
    t_name = JSONField(schema=TRANSLATION_VALUES_SCHEMA, blank=True, null=True)
    t_name_short = JSONField(schema=TRANSLATION_VALUES_SCHEMA, blank=True, null=True)

    vertical_bg = models.ForeignKey(
        "wagtailimages.Image",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="+",
        verbose_name="vertical",
    )
    horizontal_bg = models.ForeignKey(
        "wagtailimages.Image",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="+",
        verbose_name="horizontal",
    )
    ticket_link = models.URLField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    search_fields = [
        index.SearchField("title", partial_match=True),
        index.SearchField("four_letters_name", partial_match=False),
    ]

    panels = [
        FieldPanel("title"),
        FieldPanel("club"),
        MultiFieldPanel(
            [
                FieldPanel("introduction"),
                FieldPanel("phone_number"),
                FieldPanel("four_letters_name"),
                FieldPanel("ticket_link"),
            ],
            heading="Information",
        ),
        MultiFieldPanel(
            [InlinePanel("club_mascots")],
            heading="Mascots",
        ),
        MultiFieldPanel(
            [InlinePanel("club_videos")],
            heading="Videos",
        ),
        MultiFieldPanel(
            [
                FieldPanel("t_name"),
                FieldPanel("t_name_short"),
                FieldPanel("t_president"),
                FieldPanel("t_address"),
                FieldPanel("t_practice_ground"),
            ],
            heading="Translation",
        ),
        MultiFieldPanel(
            [
                FieldPanel("horizontal_bg"),
                FieldPanel("vertical_bg"),
            ],
            heading="Backgrounds",
        ),
        MultiFieldPanel(
            [InlinePanel("club_staffs")],
            heading="Staffs",
        ),
    ]

    class Meta:
        ordering = ["title"]

    def __str__(self):
        return self.title

    def get_mascots(self):
        mascots = []
        for item in self.club_mascots.all():
            mascots.append(item.mascot)
        return mascots

    def get_staffs(self):
        return self.club_staffs.all()

    def get_videos(self):
        return self.club_videos.all()

    def get_local_name(self, lang="en"):
        return self.t_name.get(lang)

    def get_manager(self, staff_id=None):
        for staff in self.get_staffs():
            if staff.position == TeamMemberTypeEnum.MANAGER.value and (
                staff_id is None
                or (staff_id is not None and staff.staff_id == staff_id)
            ):
                return staff
        return None


class ClubVideo(Orderable):
    club_extra_info = ParentalKey(
        ClubExtraInfo,
        related_name="club_videos",
        default=None,
        on_delete=models.CASCADE,
    )
    video = models.ForeignKey(
        Video,
        related_name="+",
        default=None,
        on_delete=models.CASCADE,
    )
    description = models.CharField(max_length=255, blank=True)

    panels = [
        FieldPanel("video"),
        FieldPanel("description"),
    ]


class ClubStaff(Orderable):
    MEMBER_TYPES = []
    for member_type in TeamMemberTypeEnum:
        MEMBER_TYPES.append((member_type.value, member_type.value))

    club_extra_info = ParentalKey(
        ClubExtraInfo,
        related_name="club_staffs",
        default=None,
        on_delete=models.CASCADE,
    )
    staff_id = models.IntegerField(unique=True)
    name = models.CharField(max_length=255)
    name_short = models.CharField(max_length=255)
    t_name = JSONField(schema=TRANSLATION_VALUES_SCHEMA, blank=True, null=True)
    position = models.CharField(max_length=255, choices=MEMBER_TYPES)
    nationality_name = models.CharField(max_length=255, null=True)
    avatar_image = models.ForeignKey(
        "wagtailimages.Image",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="+",
    )
    diecut_image = models.ForeignKey(
        "wagtailimages.Image",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="+",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        FieldPanel("avatar_image"),
        FieldPanel("diecut_image"),
        FieldPanel("staff_id"),
        FieldPanel("name"),
        FieldPanel("name_short"),
        FieldPanel("t_name"),
        FieldPanel("position"),
        FieldPanel("nationality_name"),
    ]

    def __str__(self):
        return f"{self.name} <id={self.staff_id}>"

    def format(self):
        data = self.__dict__
        data["avatar_image"] = self.avatar_image if self.avatar_image else None
        data["diecut_image"] = self.diecut_image if self.diecut_image else None
        if data["nationality_name"]:
            data["nationality_flag"] = get_country_flag(data["nationality_name"])
        return data


class ClubMascot(Orderable):
    club_extra_info = ParentalKey(
        ClubExtraInfo,
        related_name="club_mascots",
        default=None,
        on_delete=models.CASCADE,
    )
    mascot = models.ForeignKey(
        Mascot,
        related_name="+",
        default=None,
        on_delete=models.CASCADE,
    )

    panels = [
        FieldPanel("mascot"),
    ]


class ClubStadium(Orderable):
    club_extra_info = ParentalKey(
        ClubExtraInfo,
        related_name="club_stadiums",
        default=None,
        on_delete=models.CASCADE,
    )
    stadium = models.ForeignKey(
        StadiumExtraInfo,
        related_name="+",
        default=None,
        on_delete=models.CASCADE,
    )

    panels = [
        FieldPanel("stadium"),
    ]


class ClubPlayer(models.Model):
    club = models.ForeignKey(
        Club,
        related_name="club_players",
        on_delete=models.CASCADE,
    )
    player = models.ForeignKey(
        Player,
        related_name="player_club",
        on_delete=models.CASCADE,
    )
    year = models.IntegerField()
    position = models.CharField(max_length=10, null=True)
    jersey_no = models.IntegerField(null=True)
    is_registered = models.BooleanField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        FieldPanel("club"),
        FieldPanel("player"),
        FieldPanel("year"),
    ]


class ClubCompetition(models.Model):
    """
    source file: mst_team-x.xml
    old model: data_stadium.GameKindSeasonTeam
    """

    competition = models.ForeignKey(
        Competition,
        related_name="competition_clubs",
        on_delete=models.CASCADE,
    )
    club = models.ForeignKey(
        Club,
        related_name="club_competition",
        on_delete=models.CASCADE,
    )
    year = models.IntegerField(db_index=True)
    season_id = models.IntegerField()
    group_id = models.CharField(max_length=255, null=True)
    ordering = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        FieldPanel("club"),
        FieldPanel("competition"),
        FieldPanel("year"),
        FieldPanel("season_id"),
        FieldPanel("group_id"),
        FieldPanel("ordering"),
    ]
