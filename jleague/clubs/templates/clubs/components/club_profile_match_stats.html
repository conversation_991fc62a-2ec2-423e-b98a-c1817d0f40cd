{% load static core_tags %}

{% spaceless %}
<section class="match-stats content-container">
  <div class="header">
    <div class="league-info">
      {% if competition %}
        {% include 'components/competition-logo.html' with slug=competition.slug size='xsmall' %}
      {% endif %}

      <span class="league-name">
        {% translation_obj competition.t_name %}
      </span>
    </div>
    <div class="club-info">
      {% if club.slug == "tokushima" %}
      {% include 'components/club-emblem.html' with slug=club.slug logo_url=club.logo_club use_alt=False %}
      {% else %}
      {% include 'components/club-emblem.html' with slug=club.slug logo_url=club.logo_club use_alt=True %}
      {% endif %}

      <div class="club-links-wrapper">
        <div class="club-link-intro">
          {% translation 'CLUB_PROFILE_PAGE_VISIT_CLUB_WEBSITE' name=club|club_name %}
        </div>

        <div class="club-links">
          {% if club.website_url %}
            <a
              class="jl-button"
              href="{{ club.website_url }}"
              rel="noopener"
              target="_blank"
              onclick="sendSelectContentEvent('page', 'team-website:{{ club.slug }}');"
            >
              <span class="jl-button__label">
                {% translation 'CLUB_PROFILE_PAGE_OFFICIAL_SITE' %}
              </span>
            </a>
          {% endif %}

          {% if club.ticket_link %}
            <a
              class="jl-button jl-button--inverse"
              href="{{ club.ticket_link }}"
              rel="noopener"
              target="_blank"
              onclick="sendSelectContentEvent('page', 'team-ticket-website:{{ club.slug }}');"
            >
              <span class="jl-button__label">
                {% translation 'GLOBAL_BUY_TICKETS' %}
              </span>
            </a>
          {% endif %}
        </div>
      </div>
    </div>

    <div class="social-container">
      {% if club.facebook_url %}
        <a
          class="btn-img-circle medium no-decoration"
          href="{{ club.facebook_url }}"
          rel="noopener"
          target="_blank"
          onclick="sendSelectContentEvent('page', 'team-facebook:{{ club.slug }}');"
        >
          <svg width="11" height="22" viewBox="0 0 11 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M7.5487 21.3856H3.12471V10.9535H0.914062V7.35888H3.12471V5.20082C3.12471 2.26854 4.37229 0.523926 7.91905 0.523926H10.8711V4.11982H9.02606C7.64534 4.11982 7.5541 4.62213 7.5541 5.55962L7.54802 7.35888H10.8914L10.5001 10.9535H7.54802V21.3856H7.5487Z"
              fill="white"
            />
          </svg>
        </a>
      {% endif %}

      {% if club.twitter_url %}
        <a
          class="btn-img-circle medium no-decoration"
          href="{{ club.twitter_url }}"
          rel="noopener"
          target="_blank"
          onclick="sendSelectContentEvent('page', 'team-twitter:{{ club.slug }}');"
        >
          <svg width="19" height="17" viewBox="0 0 19 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M16.5342 2.97338C17.3252 2.47417 17.9322 1.68261 18.2171 0.739672C17.4764 1.20305 16.658 1.53817 15.7847 1.71959C15.0879 0.933807 14.0919 0.443848 12.9897 0.443848C10.8751 0.443848 9.16148 2.25231 9.16148 4.48254C9.16148 4.79917 9.19325 5.1077 9.25899 5.40237C6.07719 5.23366 3.25587 3.62743 1.36586 1.18225C1.03606 1.78084 0.847611 2.47417 0.847611 3.21373C0.847611 4.61428 1.52363 5.85073 2.55136 6.57643C1.92355 6.55563 1.33299 6.37189 0.815837 6.07144V6.12113C0.815837 8.07866 2.13501 9.71147 3.88916 10.0813C3.56704 10.176 3.22957 10.2234 2.87896 10.2234C2.63244 10.2234 2.3914 10.1991 2.15912 10.1517C2.64559 11.7557 4.06008 12.9251 5.73644 12.9563C4.42494 14.0402 2.77378 14.685 0.980186 14.685C0.67121 14.685 0.365521 14.6677 0.0664062 14.6296C1.76139 15.7747 3.77412 16.4438 5.93695 16.4438C12.982 16.4438 16.8322 10.2893 16.8322 4.9517C16.8322 4.77606 16.83 4.60157 16.8223 4.42939C17.5707 3.85969 18.2215 3.14787 18.7332 2.33782C18.0462 2.65906 17.3077 2.87631 16.5342 2.97338Z"
              fill="white"
            />
          </svg>
        </a>
      {% endif %}

      {% if club.instagram_url %}
        <a
          class="btn-img-circle medium no-decoration"
          href="{{ club.instagram_url }}"
          rel="noopener"
          target="_blank"
          onclick="sendSelectContentEvent('page', 'team-instagram:{{ club.slug }}');"
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M10.0001 0C7.28447 0 6.94374 0.0116967 5.87731 0.0605179C4.81291 0.10883 4.08569 0.278179 3.45 0.525336C2.79194 0.78063 2.23457 1.12238 1.67872 1.67874C1.12237 2.23458 0.780624 2.79247 0.525332 3.45003C0.278177 4.08623 0.10883 4.81295 0.0605174 5.87735C0.0116966 6.94379 0 7.28452 0 10.0002C0 12.7164 0.0116966 13.0566 0.0605174 14.1235C0.10883 15.188 0.278177 15.9147 0.525332 16.5509C0.780624 17.2084 1.12237 17.7663 1.67872 18.3222C2.23457 18.878 2.79194 19.2198 3.45 19.4756C4.08569 19.7227 4.81291 19.8921 5.87731 19.9404C6.94374 19.9892 7.28447 20.0004 10.0001 20.0004C12.7163 20.0004 13.0565 19.9892 14.1234 19.9404C15.1878 19.8921 15.9146 19.7227 16.5508 19.4756C17.2083 19.2198 17.7657 18.878 18.322 18.3222C18.8779 17.7663 19.2196 17.2084 19.4754 16.5509C19.7226 15.9147 19.8914 15.188 19.9402 14.1235C19.9885 13.0566 20.0002 12.7164 20.0002 10.0002C20.0002 7.28452 19.9885 6.94379 19.9402 5.87735C19.8914 4.81295 19.7226 4.08623 19.4754 3.45003C19.2196 2.79247 18.8779 2.23458 18.322 1.67874C17.7657 1.12238 17.2083 0.78063 16.5508 0.525336C15.9146 0.278179 15.1878 0.10883 14.1234 0.0605179C13.0565 0.0116967 12.7163 0 10.0001 0ZM10.0026 1.80176C12.673 1.80176 12.9893 1.81193 14.0435 1.86024C15.0184 1.90448 15.5478 2.06773 15.9007 2.20453C16.3671 2.38608 16.7002 2.60272 17.0501 2.95261C17.4005 3.30249 17.6166 3.63559 17.7982 4.10244C17.9355 4.45486 18.0982 4.98427 18.1424 5.95967C18.1908 7.01389 18.2009 7.33021 18.2009 10.0001C18.2009 12.6705 18.1908 12.9868 18.1424 14.041C18.0982 15.0164 17.9355 15.5458 17.7982 15.8978C17.6166 16.3651 17.4005 16.6977 17.0501 17.0476C16.7002 17.3975 16.3671 17.6146 15.9007 17.7957C15.5478 17.933 15.0184 18.0957 14.0435 18.1405C12.9893 18.1883 12.673 18.1984 10.0026 18.1984C7.33214 18.1984 7.01633 18.1883 5.9616 18.1405C4.9867 18.0957 4.4573 17.933 4.10487 17.7957C3.63802 17.6146 3.30492 17.3975 2.95503 17.0476C2.60515 16.6977 2.38851 16.3651 2.20695 15.8978C2.07015 15.5458 1.90691 15.0164 1.86266 14.041C1.81435 12.9868 1.80469 12.6705 1.80469 10.0001C1.80469 7.33021 1.81435 7.01389 1.86266 5.95967C1.90691 4.98427 2.07015 4.45486 2.20695 4.10244C2.38851 3.63559 2.60515 3.30249 2.95503 2.95261C3.30492 2.60272 3.63802 2.38608 4.10487 2.20453C4.4573 2.06773 4.9867 1.90448 5.9616 1.86024C7.01633 1.81193 7.33265 1.80176 10.0026 1.80176ZM10.0049 13.3342C8.16394 13.3342 6.67188 11.8416 6.67188 10.0006C6.67188 8.15961 8.16394 6.66748 10.0049 6.66748C11.8458 6.66748 13.3384 8.15961 13.3384 10.0006C13.3384 11.8416 11.8458 13.3342 10.0049 13.3342ZM10.0026 4.86475C7.16687 4.86475 4.86719 7.16336 4.86719 9.99951C4.86719 12.8357 7.16687 15.1348 10.0026 15.1348C12.8388 15.1348 15.1379 12.8357 15.1379 9.99951C15.1379 7.16336 12.8388 4.86475 10.0026 4.86475ZM15.3408 5.86167C16.0034 5.86167 16.5409 5.32466 16.5409 4.66154C16.5409 3.99892 16.0034 3.46191 15.3408 3.46191C14.6776 3.46191 14.1406 3.99892 14.1406 4.66154C14.1406 5.32466 14.6776 5.86167 15.3408 5.86167Z"
              fill="white"
            />
          </svg>
        </a>
      {% endif %}
    </div>
  </div>

  <div id="match-stats-list" class="stat-list">
    {% include 'clubs/components/club_profile_next_match.html' %}
    {% include 'clubs/components/club_profile_last_5_match.html' %}
    {% include 'clubs/components/club_profile_standing.html' %}
  </div>
</section>
{% endspaceless %}
